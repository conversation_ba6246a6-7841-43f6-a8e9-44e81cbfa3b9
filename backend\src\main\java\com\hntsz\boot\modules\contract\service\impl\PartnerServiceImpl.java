package com.hntsz.boot.modules.contract.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.modules.contract.converter.PartnerConverter;
import com.hntsz.boot.modules.contract.mapper.PartnerMapper;
import com.hntsz.boot.modules.contract.model.entity.Partner;
import com.hntsz.boot.modules.contract.model.form.PartnerForm;
import com.hntsz.boot.modules.contract.model.query.PartnerQuery;
import com.hntsz.boot.modules.contract.model.vo.PartnerVO;
import com.hntsz.boot.modules.contract.service.PartnerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 业务伙伴服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@RequiredArgsConstructor
public class PartnerServiceImpl extends ServiceImpl<PartnerMapper, Partner> implements PartnerService {

    private final PartnerMapper partnerMapper;

    @Override
    public IPage<PartnerVO> getPartnerPage(PartnerQuery query) {
        Page<PartnerVO> page = new Page<>(query.getPageNum(), query.getPageSize());
        return partnerMapper.getPartnerPage(page, query);
    }

    @Override
    public PartnerVO getPartnerDetail(Long id) {
        return partnerMapper.getPartnerDetail(id);
    }

    @Override
    public PartnerForm getPartnerFormData(Long id) {
        Partner entity = this.getById(id);
        return PartnerConverter.INSTANCE.entityToForm(entity);
    }

    @Override
    public boolean savePartner(PartnerForm form) {
        Partner entity = PartnerConverter.INSTANCE.formToEntity(form);
        return this.save(entity);
    }

    @Override
    public boolean updatePartner(Long id, PartnerForm form) {
        Partner entity = PartnerConverter.INSTANCE.formToEntity(form);
        entity.setId(id);
        return this.updateById(entity);
    }

    @Override
    public boolean deletePartners(Long[] ids) {
        return this.removeByIds(Arrays.asList(ids));
    }

    @Override
    public List<Option> getPartnerOptions() {
        return partnerMapper.getPartnerOptions();
    }

    @Override
    public Partner getByPartnerName(String partnerName) {
        return partnerMapper.getByPartnerName(partnerName);
    }

    @Override
    public Partner getByPartnerCode(String partnerCode) {
        return partnerMapper.getByPartnerCode(partnerCode);
    }

    @Override
    public boolean existsByPartnerName(String partnerName, Long excludeId) {
        LambdaQueryWrapper<Partner> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Partner::getPartnerName, partnerName);
        if (excludeId != null) {
            wrapper.ne(Partner::getId, excludeId);
        }
        return this.count(wrapper) > 0;
    }

    @Override
    public boolean existsByPartnerCode(String partnerCode, Long excludeId) {
        if (StrUtil.isBlank(partnerCode)) {
            return false;
        }
        
        LambdaQueryWrapper<Partner> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Partner::getPartnerCode, partnerCode);
        if (excludeId != null) {
            wrapper.ne(Partner::getId, excludeId);
        }
        return this.count(wrapper) > 0;
    }
}
