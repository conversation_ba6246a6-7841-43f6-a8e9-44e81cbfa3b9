<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.UserRoleMapper">

    <!-- 根据用户ID获取角色ID集合 -->
    <select id="listRoleIdsByUserId" resultType="java.lang.Long">
        SELECT
            role_id
        FROM
            sys_user_role
        WHERE
            user_id = #{userId}
    </select>

    <!-- 获取角色绑定的用户数 -->
    <select id="countUsersForRole" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            sys_user_role t1
            INNER JOIN sys_role t2 ON t1.role_id = t2.id AND t2.is_deleted = 0
            INNER JOIN sys_user t3 ON t1.user_id = t3.id
            AND t3.is_deleted = 0
        WHERE
            t1.role_id = #{roleId}
    </select>
</mapper>
