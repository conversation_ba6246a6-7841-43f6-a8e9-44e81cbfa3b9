package com.hntsz.boot.modules.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同主表实体对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@TableName("contract")
public class Contract extends BaseEntityExtra {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同类型(关联字典编码：contract_type)
     */
    private String contractType;

    /**
     * 合同分类(关联字典编码：contract_category)
     */
    private String contractCategory;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 关联商机ID(关联opportunity表)
     */
    private Long opportunityId;

    /**
     * 签署日期
     */
    private LocalDate signingDate;

    /**
     * 生效日期
     */
    private LocalDate effectiveDate;

    /**
     * 到期日期
     */
    private LocalDate expiryDate;

    /**
     * 合同状态(draft-草稿 pending-待签署 active-已生效 completed-已完成 terminated-已终止 cancelled-已作废)
     */
    private String contractStatus;

    /**
     * 付款方式(关联字典编码：payment_method)
     */
    private String paymentMethod;

    /**
     * 签署地点
     */
    private String signingLocation;

    /**
     * 负责人ID(关联sys_user表)
     */
    private Long responsibleUserId;

    /**
     * 所属部门ID(关联sys_dept表)
     */
    private Long deptId;

    /**
     * 备注
     */
    private String remark;
}
