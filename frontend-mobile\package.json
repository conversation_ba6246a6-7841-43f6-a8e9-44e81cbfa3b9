{"name": "tsz-mobile", "version": "1.0.0", "description": "TSZ Mobile App - 移动端企业管理平台", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.21", "vue-router": "^4.3.0", "pinia": "^2.1.7", "axios": "^1.6.7", "vant": "^4.8.11", "@vant/touch-emulator": "^1.4.0", "@vueuse/core": "^10.9.0", "dayjs": "^1.11.10", "nprogress": "^0.2.0"}, "devDependencies": {"@types/node": "^20.11.25", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.0.4", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.22.0", "prettier": "^3.2.5", "typescript": "~5.4.0", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.1.5", "vite-plugin-mock": "^3.0.1", "vue-tsc": "^2.0.6", "postcss": "^8.4.35", "postcss-px-to-viewport": "^1.1.1", "amfe-flexible": "^2.2.1"}, "engines": {"node": ">=18.0.0"}}