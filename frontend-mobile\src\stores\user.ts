// 用户状态管理
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { authApi } from '@/api/auth'
import type { UserInfo, LoginForm, LoginResult } from '@/types'
import { getToken, setToken, removeToken, getRefreshToken, setRefreshToken, clearAuth } from '@/utils/auth'
import { localStorage, STORAGE_KEYS } from '@/utils/storage'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string | null>(getToken())
  const refreshToken = ref<string | null>(getRefreshToken())
  const userInfo = ref<UserInfo | null>(localStorage.get(STORAGE_KEYS.USER_INFO))

  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const userId = computed(() => userInfo.value?.id)
  const username = computed(() => userInfo.value?.username)
  const nickname = computed(() => userInfo.value?.nickname)
  const avatar = computed(() => userInfo.value?.avatar)
  const roles = computed(() => userInfo.value?.roles || [])
  const permissions = computed(() => userInfo.value?.permissions || [])

  // 方法
  /**
   * 登录
   */
  const login = async (loginForm: LoginForm): Promise<void> => {
    try {
      const result: LoginResult = await authApi.login(loginForm)
      
      // 保存token
      token.value = result.accessToken
      refreshToken.value = result.refreshToken
      
      // 设置过期时间
      const expire = result.expiresIn * 1000
      setToken(result.accessToken, expire)
      setRefreshToken(result.refreshToken, expire * 2) // 刷新token有效期是访问token的2倍

      // 获取用户信息
      await getUserInfo()
    } catch (error) {
      throw error
    }
  }

  /**
   * 退出登录
   */
  const logout = async (): Promise<void> => {
    try {
      // 调用退出登录API
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout API failed:', error)
    } finally {
      // 清除本地状态
      token.value = null
      refreshToken.value = null
      userInfo.value = null
      
      // 清除本地存储
      clearAuth()
    }
  }

  /**
   * 获取用户信息
   */
  const getUserInfo = async (): Promise<void> => {
    if (!token.value) {
      throw new Error('Token is required')
    }

    try {
      const result = await authApi.getUserInfo()
      userInfo.value = result
      
      // 保存到本地存储
      localStorage.set(STORAGE_KEYS.USER_INFO, result)
    } catch (error) {
      // 如果获取用户信息失败，清除token
      await logout()
      throw error
    }
  }

  /**
   * 刷新token
   */
  const refreshAccessToken = async (): Promise<string> => {
    if (!refreshToken.value) {
      throw new Error('Refresh token is required')
    }

    try {
      const result = await authApi.refreshToken(refreshToken.value)
      
      // 更新token
      token.value = result.accessToken
      refreshToken.value = result.refreshToken
      
      // 设置过期时间
      const expire = result.expiresIn * 1000
      setToken(result.accessToken, expire)
      setRefreshToken(result.refreshToken, expire * 2)
      
      return result.accessToken
    } catch (error) {
      // 刷新失败，清除所有认证信息
      await logout()
      throw error
    }
  }

  /**
   * 检查权限
   */
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  /**
   * 检查角色
   */
  const hasRole = (role: string): boolean => {
    return roles.value.includes(role)
  }

  /**
   * 检查是否有任一权限
   */
  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  /**
   * 检查是否有任一角色
   */
  const hasAnyRole = (roleList: string[]): boolean => {
    return roleList.some(role => hasRole(role))
  }

  return {
    // 状态
    token: readonly(token),
    refreshToken: readonly(refreshToken),
    userInfo: readonly(userInfo),
    
    // 计算属性
    isLoggedIn,
    userId,
    username,
    nickname,
    avatar,
    roles,
    permissions,
    
    // 方法
    login,
    logout,
    getUserInfo,
    refreshAccessToken,
    hasPermission,
    hasRole,
    hasAnyPermission,
    hasAnyRole
  }
})