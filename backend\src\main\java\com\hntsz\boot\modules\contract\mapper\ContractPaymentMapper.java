package com.hntsz.boot.modules.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hntsz.boot.modules.contract.model.entity.ContractPayment;
import com.hntsz.boot.modules.contract.model.query.ContractPaymentQuery;
import com.hntsz.boot.modules.contract.model.vo.ContractPaymentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 合同付款记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface ContractPaymentMapper extends BaseMapper<ContractPayment> {

    /**
     * 获取合同付款记录分页列表
     *
     * @param page 分页参数
     * @param query 查询参数
     * @return 合同付款记录分页列表
     */
    IPage<ContractPaymentVO> getContractPaymentPage(IPage<ContractPaymentVO> page, @Param("query") ContractPaymentQuery query);

    /**
     * 获取合同付款记录详情
     *
     * @param id 付款记录ID
     * @return 合同付款记录详情
     */
    ContractPaymentVO getContractPaymentDetail(@Param("id") Long id);

    /**
     * 根据合同ID获取付款记录列表
     *
     * @param contractId 合同ID
     * @return 付款记录列表
     */
    List<ContractPaymentVO> getByContractId(@Param("contractId") Long contractId);

    /**
     * 根据付款单号查询付款记录
     *
     * @param paymentNo 付款单号
     * @return 付款记录
     */
    ContractPayment getByPaymentNo(@Param("paymentNo") String paymentNo);

    /**
     * 根据合同ID统计付款总额
     *
     * @param contractId 合同ID
     * @return 付款总额
     */
    BigDecimal getTotalAmountByContractId(@Param("contractId") Long contractId);

    /**
     * 根据合同ID和付款状态统计付款金额
     *
     * @param contractId 合同ID
     * @param paymentStatus 付款状态
     * @return 付款金额
     */
    BigDecimal getAmountByContractIdAndStatus(@Param("contractId") Long contractId, @Param("paymentStatus") String paymentStatus);

    /**
     * 根据伙伴ID获取付款记录（作为付款方）
     *
     * @param partnerId 伙伴ID
     * @return 付款记录列表
     */
    List<ContractPaymentVO> getByPayerPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 根据伙伴ID获取付款记录（作为收款方）
     *
     * @param partnerId 伙伴ID
     * @return 付款记录列表
     */
    List<ContractPaymentVO> getByPayeePartnerId(@Param("partnerId") Long partnerId);
}
