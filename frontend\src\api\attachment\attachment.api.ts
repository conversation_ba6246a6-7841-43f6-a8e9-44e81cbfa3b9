import request from "@/utils/request";

const ATTACHMENT_BASE_URL = "/api/v1/attachment";

const AttachmentAPI = {
  /**
   * 获取附件列表
   *
   * @param queryParams 查询参数（可选）
   * @returns 附件列表数据
   */
  getList(queryParams?: AttachmentQuery) {
    return request<any, AttachmentVO[]>({
      url: `${ATTACHMENT_BASE_URL}`,
      method: "get",
      params: queryParams,
    });
  },

  /**
   * 获取附件详情
   *
   * @param id 附件ID
   * @returns 附件详情数据
   */
  getDetail(id: string) {
    return request<any, AttachmentVO>({
      url: `${ATTACHMENT_BASE_URL}/${id}`,
      method: "get",
    });
  },

  /**
   * 上传附件
   *
   * @param file 文件对象
   * @returns 上传结果
   */
  upload(file: File) {
    const formData = new FormData();
    formData.append("file", file);
    return request<any, AttachmentVO>({
      url: `${ATTACHMENT_BASE_URL}/upload`,
      method: "post",
      data: formData,
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /**
   * 修改附件
   *
   * @param id 附件ID
   * @param data 附件表单数据
   * @returns 请求结果
   */
  update(id: string, data: AttachmentForm) {
    return request({
      url: `${ATTACHMENT_BASE_URL}/${id}`,
      method: "put",
      data: data,
    });
  },

  /**
   * 删除附件
   *
   * @param ids 附件ID，多个以英文逗号(,)分隔
   * @returns 请求结果
   */
  deleteByIds(ids: string) {
    return request({
      url: `${ATTACHMENT_BASE_URL}/${ids}`,
      method: "delete",
    });
  },
};

export default AttachmentAPI;

/** 附件查询参数 */
export interface AttachmentQuery {
  /** 搜索关键字 */
  keywords?: string;
  /** 状态 */
  status?: number;
}

/** 附件类型 */
export interface AttachmentVO {
  /** 创建时间 */
  createTime?: Date;
  /** 附件ID */
  id?: string;
  /** 附件名称 */
  fileName?: string;
  /** 附件路径 */
  filePath?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 文件类型 */
  fileType?: string;
  /** 状态(1:启用；0:禁用) */
  status?: number;
  /** 修改时间 */
  updateTime?: Date;
}

/** 附件表单类型 */
export interface AttachmentForm {
  /** 附件ID(新增不填) */
  id?: string;
  /** 附件名称 */
  fileName?: string;
  /** 附件路径 */
  filePath?: string;
  /** 文件大小 */
  fileSize?: number;
  /** 文件类型 */
  fileType?: string;
  /** 状态(1:启用；0：禁用) */
  status?: number;
}
