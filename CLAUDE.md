# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack integrated platform with a Java Spring Boot backend and Vue3 frontend. The project implements an enterprise-level management system with features like user management, contract management, opportunity tracking, and business process automation.

## Development Commands

### Frontend (Vue3 + Element Plus)
```bash
# Development
cd frontend
pnpm install
pnpm run dev

# Build
pnpm run build

# Linting and Type Checking
pnpm run lint              # Run all linting (ESLint, Prettier, StyleLint)
pnpm run lint:eslint       # ESLint only
pnpm run lint:prettier     # Prettier only
pnpm run lint:stylelint    # StyleLint only
pnpm run type-check        # TypeScript type checking

# Testing
pnpm run preview           # Preview production build
```

### Backend (Java Spring Boot)
```bash
# Development
cd backend
mvn clean install
mvn spring-boot:run

# Build
mvn clean package

# Testing
mvn test
```

## Project Architecture

### Frontend Structure
- **Vue 3** with Composition API and TypeScript
- **Element Plus** for UI components
- **Pinia** for state management
- **Vue Router** for routing
- **Vite** for build tooling
- **UnoCSS** for styling

### Backend Structure
- **Spring Boot 3** with Java 17
- **Spring Security** for authentication/authorization
- **MyBatis Plus** for ORM
- **MySQL** for database
- **Redis** for caching
- **Knife4j** for API documentation

### Key Modules

#### Frontend (`frontend/src/`)
- `api/` - API service definitions
- `components/` - Reusable Vue components
- `views/` - Page components
- `store/` - Pinia store modules
- `router/` - Route definitions
- `utils/` - Utility functions
- `types/` - TypeScript type definitions

#### Backend (`backend/src/main/java/com/hntsz/boot/`)
- `common/` - Common utilities, annotations, and base classes
- `config/` - Configuration classes
- `core/` - Core functionality (security, aspects, filters)
- `modules/` - Business modules (contract, opportunity, attachment)
- `shared/` - Shared services (auth, file, codegen, mail, sms)
- `system/` - System management (user, role, menu, dept, dict)

### Development Environment
- **Node.js**: 18+ (frontend)
- **Java**: 17+ (backend)
- **MySQL**: 8.0+
- **Redis**: 6.0+

### Key Features
- **Authentication**: JWT-based with Spring Security
  - Username/password login
  - SMS verification login
  - WeChat authorization login
  - OAuth 2.0 universal provider login
- **Authorization**: Role-based access control with data permissions
- **File Management**: Multiple storage options (local, MinIO, Aliyun OSS)
- **Code Generation**: Automated CRUD code generation
- **WebSocket**: Real-time notifications
- **Internationalization**: Multi-language support
- **API Documentation**: Knife4j/Swagger integration

### Database Schema
- Contract management with partner relationships
- Opportunity tracking with follow-up records
- User/role/permission management
- System configuration and dictionaries
- Attachment management

### Configuration Files
- Frontend: `frontend/.env.development`, `frontend/vite.config.ts`
- Backend: `backend/src/main/resources/application-dev.yml`

### OAuth Configuration
OAuth 2.0 login is configured in the backend application properties:
```yaml
security:
  oauth:
    client-id: your-oauth-client-id
    authorization-url: https://your-provider.com/oauth/authorize
    redirect-uri: http://localhost:3000/#/auth/callback
    scopes: openid profile email
    logout-url: https://your-provider.com/oauth/logout
```
The frontend automatically fetches OAuth configuration from the backend API.

### Build and Deployment
- Frontend builds to `frontend/dist/`
- Backend builds to `backend/target/`
- Docker configuration available in `backend/docker/`

## Important Notes

- Frontend uses pnpm as package manager (not npm)
- Backend uses Maven for dependency management
- The project has separate dev/staging/prod configurations
- API documentation available at `/doc.html` when backend is running
- Mock data support available for frontend development