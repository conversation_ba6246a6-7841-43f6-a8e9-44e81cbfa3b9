SET NAMES utf8mb4;
-- =====================================================================================================================
-- 1. 商机线索表 (opportunity) - 详细示例数据
-- 涵盖所有商机阶段 (initial, interested, proposal, negotiation, closed_won, closed_lost) 和状态
-- =====================================================================================================================
DELETE FROM `opportunity`;
INSERT INTO `opportunity` (`id`, `opportunity_code`, `opportunity_name`, `opportunity_type`, `opportunity_source`, `partner_id`, `contact_person`, `contact_phone`, `contact_email`, `opportunity_stage`, `win_probability`, `estimated_amount`, `estimated_close_date`, `actual_close_date`, `opportunity_status`, `lost_reason`, `priority`, `product_interest`, `requirements`, `competition_info`, `next_action`, `next_follow_date`, `responsible_user_id`, `dept_id`, `tags`, `archive_reason`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES
-- Active 商机 - 不同阶段
(1, 'OPP-2024-001', '智慧园区管理系统项目', 'new_customer', 'exhibition', 3, '周八', '***********', '<EMAIL>', 'negotiation', 80, 2800000.00, '2024-08-15', NULL, 'active', NULL, 'high', '智慧园区管理平台,物联网设备集成', '需要一套完整的园区智能化管理系统，包含访客管理、设备监控、能耗管理等功能', '主要竞争对手：华为、海康威视', '准备最终报价方案，下周三进行技术方案演示', '2024-07-18', 1, 2, '大客户,长期合作', NULL, '北京卓越客户集团的重点项目，预计Q3签约', 1, NOW(), 1, NOW(), 0),

(2, 'OPP-2024-002', '数字化办公系统升级', 'existing_customer', 'phone', 7, '张科长', '0755-88886666', '<EMAIL>', 'proposal', 60, 450000.00, '2024-09-30', NULL, 'active', NULL, 'medium', 'OA系统,工作流引擎', '现有办公系统需要升级，增加移动端支持和智能审批流程', '竞争对手：金蝶、用友', '完善技术方案，准备用户培训计划', '2024-07-20', 2, 2, '政府项目,稳定', NULL, 'XX市政府数据中心的办公系统升级需求', 1, NOW(), 1, NOW(), 0),

(3, 'OPP-2024-003', '电商平台定制开发', 'new_customer', 'website', NULL, '李总', '18800188888', '<EMAIL>', 'interested', 40, 680000.00, '2024-10-15', NULL, 'active', NULL, 'medium', '电商平台,支付系统,库存管理', '需要定制开发一套B2B电商平台，支持多商户入驻', '主要竞争对手：Shopify Plus、Magento', '发送详细产品资料，安排产品演示', '2024-07-22', 3, 3, '电商,B2B', NULL, '新接触的电商客户，有一定实力', 1, NOW(), 1, NOW(), 0),

(4, 'OPP-2024-004', '移动应用开发外包', 'partnership', 'referral', 8, '黄经理', '18800188000', '<EMAIL>', 'initial', 20, 280000.00, '2024-11-30', NULL, 'active', NULL, 'low', 'Flutter应用,React Native', '为孵化器内创业团队提供移动应用开发服务', '内部团队技术能力有限', '了解具体需求，制定合作方案', '2024-07-25', 3, 3, '合作伙伴,移动端', NULL, '创业孵化器推荐的合作机会', 1, NOW(), 1, NOW(), 0),

-- Won 商机 - 已成交
(5, 'OPP-2023-015', '企业官网重构项目', 'existing_customer', 'cold_call', 2, '赵六', '***********', '<EMAIL>', 'closed_won', 100, 150000.00, '2024-02-28', '2024-02-25', 'won', NULL, 'medium', '企业官网,响应式设计', '官网技术栈老旧，需要重新设计和开发', '无主要竞争对手', '项目已完成，客户满意', NULL, 1, 2, '官网,重构', NULL, '上海优品供应商的官网重构项目，已成功交付', 1, NOW(), 1, NOW(), 0),

(6, 'OPP-2023-020', '库存管理系统', 'upsell', 'existing_customer', 3, '孙七', '***********', '<EMAIL>', 'closed_won', 100, 320000.00, '2023-12-15', '2023-12-10', 'won', NULL, 'high', '库存管理,进销存', '在原有ERP基础上增加高级库存管理功能', '内部开发 vs 外包', '项目成功上线，获得客户好评', NULL, 2, 2, '老客户,增值服务', NULL, '北京卓越客户的追加订单，合作愉快', 1, NOW(), 1, NOW(), 0),

-- Lost 商机 - 已失败（可以归档）
(7, 'OPP-2024-005', '在线教育平台开发', 'new_customer', 'social_media', NULL, '王校长', '13600136666', '<EMAIL>', 'closed_lost', 0, 960000.00, '2024-06-30', '2024-07-01', 'lost', 'price_high', 'high', '在线教育,直播系统', '需要开发一套完整的在线教育平台，支持直播授课', '腾讯课堂、钉钉', '客户选择了价格更低的方案', NULL, 2, 2, '教育,直播', NULL, '价格因素败给竞争对手，技术方案获得认可', 1, NOW(), 1, NOW(), 0),

(8, 'OPP-2024-006', '医疗信息系统集成', 'new_customer', 'exhibition', NULL, '陈主任', '0755-82345678', '<EMAIL>', 'closed_lost', 0, 1200000.00, '2024-05-31', '2024-05-28', 'lost', 'no_budget', 'high', '医疗信息系统,数据集成', '多个医疗系统需要数据打通和集成', '东软、卫宁健康', '客户预算削减，项目暂停', NULL, 1, 2, '医疗,集成', NULL, '预算问题导致项目暂停，保持后续联系', 1, NOW(), 1, NOW(), 0),

-- Cancelled 商机 - 已取消（可以归档）
(9, 'OPP-2024-007', '智能制造执行系统', 'new_customer', 'advertisement', NULL, '刘厂长', '13500135555', '<EMAIL>', 'proposal', 50, 1500000.00, '2024-12-31', NULL, 'cancelled', NULL, 'high', 'MES系统,工业物联网', '工厂数字化改造，需要MES系统支持生产管理', '西门子、施耐德', '客户内部预算审批延期到明年', '2024-09-01', 1, 2, '制造业,MES', NULL, '制造业客户，项目有潜力但决策周期长，客户暂时取消需求', 1, NOW(), 1, NOW(), 0),

(10, 'OPP-2024-008', '区块链溯源系统', 'new_customer', 'partnership', NULL, '张总', '18600186666', '<EMAIL>', 'initial', 10, 800000.00, '2024-08-30', NULL, 'cancelled', NULL, 'low', '区块链,产品溯源', '食品行业产品溯源系统，基于区块链技术', '蚂蚁链、腾讯区块链', '客户公司业务调整，取消该项目', NULL, 3, 3, '区块链,溯源', NULL, '客户公司战略调整，项目需求取消', 1, NOW(), 1, NOW(), 0),

-- Archived 商机 - 已归档（示例数据）
(13, 'OPP-2024-011', '旧ERP系统升级项目', 'existing_customer', 'phone', 3, '周八', '***********', '<EMAIL>', 'closed_lost', 0, 800000.00, '2024-05-30', '2024-05-25', 'archived', 'competitor', 'medium', 'ERP系统,企业管理', '对现有ERP系统进行全面升级改造', '用友、金蝶', '客户最终选择了其他供应商', NULL, 1, 2, '老客户,ERP', '由于价格和服务因素，客户选择了竞争对手的方案。项目跟进超过6个月，多次商务谈判未能成功，现予以归档处理。', '项目跟进时间较长，客户最终选择竞争对手，现归档处理', 1, NOW(), 1, NOW(), 0),

(14, 'OPP-2024-012', '移动端APP开发项目', 'new_customer', 'website', NULL, '李经理', '18900189999', '<EMAIL>', 'initial', 10, 200000.00, '2024-04-30', NULL, 'archived', NULL, 'low', '移动应用,创业项目', '创业公司需要开发移动端应用', '无主要竞争对手', '客户资金链断裂，项目取消', NULL, 3, 3, '创业,移动端', '客户公司资金链出现问题，无法继续推进项目，现归档处理。', '创业公司资金问题导致项目无法继续，现归档', 1, NOW(), 1, NOW(), 0),

-- 更多不同类型的商机
(11, 'OPP-2024-009', '客户关系管理系统', 'cross_sell', 'referral', 3, '周八', '***********', '<EMAIL>', 'interested', 45, 380000.00, '2024-09-15', NULL, 'active', NULL, 'medium', 'CRM系统,客户管理', '在现有系统基础上增加CRM功能模块', '销售易、纷享销客', '了解具体业务流程，定制化方案设计', '2024-07-19', 2, 2, '老客户,CRM', NULL, '北京卓越客户的CRM需求，交叉销售机会', 1, NOW(), 1, NOW(), 0),

(12, 'OPP-2024-010', '数据分析平台', 'renewal', 'existing_customer', 7, '张科长', '0755-88886666', '<EMAIL>', 'negotiation', 70, 650000.00, '2024-08-31', NULL, 'active', NULL, 'high', '数据分析,报表系统', '现有数据平台合同到期，需要续约并升级功能', '帆软、永洪科技', '准备续约方案，增加AI分析功能', '2024-07-21', 1, 2, '续约,数据分析', NULL, '政府数据中心的续约项目，关系良好', 1, NOW(), 1, NOW(), 0);

-- =====================================================================================================================
-- 2. 商机跟进记录表 (opportunity_follow) - 详细示例数据
-- 涵盖所有跟进方式和结果类型
-- =====================================================================================================================
DELETE FROM `opportunity_follow`;
INSERT INTO `opportunity_follow` (`id`, `opportunity_id`, `follow_type`, `follow_date`, `follow_duration`, `contact_person`, `follow_content`, `follow_result`, `next_action`, `next_follow_date`, `attachment_id`, `follow_user_id`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES
-- 商机1的跟进记录 (智慧园区管理系统) - 进展顺利
(1, 1, 'phone', '2024-07-01 09:30:00', 45, '周八', '电话沟通项目需求，了解具体功能要求和预算范围。客户对我们的技术方案很感兴趣，希望看到详细的技术架构图。', 'positive', '准备技术方案PPT，安排下周现场演示', '2024-07-08', NULL, 1, '客户态度积极，项目推进顺利', 1, NOW(), 1, NOW()),
(2, 1, 'visit', '2024-07-08 14:00:00', 120, '周八', '现场拜访，进行技术方案演示。展示了类似项目案例，客户对技术实力认可。讨论了项目时间节点和人员配置。', 'interested', '根据反馈优化方案，准备商务报价', '2024-07-15', '1,2', 1, '现场演示效果良好，客户专业团队参与讨论', 1, NOW(), 1, NOW()),
(3, 1, 'email', '2024-07-15 16:20:00', 30, '周八', '发送正式商务报价单和项目实施计划。价格在客户预算范围内，实施周期6个月。', 'considering', '等待客户内部评审，准备最终谈判', '2024-07-18', '3', 1, '报价已发送，等待客户反馈', 1, NOW(), 1, NOW()),

-- 商机2的跟进记录 (数字化办公系统升级) - 政府项目流程
(4, 2, 'phone', '2024-06-20 10:00:00', 60, '张科长', '政府采购需求沟通，了解现有系统情况和升级需求。需要支持移动办公和无纸化审批。', 'need_more_info', '准备现状调研方案，申请进场调研', '2024-06-25', NULL, 2, '政府项目流程相对复杂，需要详细调研', 1, NOW(), 1, NOW()),
(5, 2, 'visit', '2024-06-25 09:00:00', 180, '张科长', '现场调研政府现有系统架构，了解业务流程和用户使用习惯。收集了详细需求文档。', 'positive', '基于调研结果设计技术方案', '2024-07-05', '4,5', 2, '调研工作顺利完成，获得第一手资料', 1, NOW(), 1, NOW()),
(6, 2, 'meeting', '2024-07-05 15:00:00', 90, '张科长', '技术方案评审会议，政府技术团队参与评审。方案整体获得认可，部分细节需要调整。', 'interested', '根据评审意见修改方案，准备投标文件', '2024-07-20', '6', 2, '技术方案通过评审，进入投标准备阶段', 1, NOW(), 1, NOW()),

-- 商机3的跟进记录 (电商平台定制开发) - 新客户培育
(7, 3, 'website', '2024-06-15 11:30:00', 20, '李总', '客户通过官网表单提交需求，电话回访了解基本情况。客户是传统贸易公司，希望转型电商。', 'interested', '发送公司介绍和电商解决方案资料', '2024-06-20', NULL, 3, '新客户，有转型需求但对线上化了解有限', 1, NOW(), 1, NOW()),
(8, 3, 'email', '2024-06-20 09:00:00', 15, '李总', '发送电商平台解决方案和成功案例。包含功能清单、技术架构和价格区间。', 'considering', '等待客户反馈，准备演示环境', '2024-06-28', '7', 3, '已发送详细方案，客户需要时间消化', 1, NOW(), 1, NOW()),
(9, 3, 'demo', '2024-06-28 14:00:00', 60, '李总', '在线产品演示，展示电商平台核心功能。客户对多商户管理和订单处理流程比较感兴趣。', 'need_more_info', '提供更详细的功能说明文档', '2024-07-22', NULL, 3, '演示效果不错，客户需要更多技术细节', 1, NOW(), 1, NOW()),

-- 商机5的跟进记录 (企业官网重构) - 已成交项目的历史记录
(10, 5, 'cold_call', '2023-11-15 10:30:00', 25, '赵六', '主动联系上海优品供应商，了解到他们官网确实需要升级。技术栈老旧，移动端体验差。', 'interested', '发送官网重构方案和报价', '2023-11-20', NULL, 1, '陌生开发成功，客户有明确需求', 1, NOW(), 1, NOW()),
(11, 5, 'proposal', '2023-11-20 16:00:00', 30, '赵六', '发送官网重构技术方案，包含响应式设计、SEO优化、后台管理系统等。', 'positive', '准备合同谈判，确定项目细节', '2023-11-25', '8', 1, '方案获得客户认可，准备签约', 1, NOW(), 1, NOW()),
(12, 5, 'meeting', '2023-11-25 14:30:00', 120, '赵六', '合同谈判会议，确定项目范围、时间节点和付款方式。双方达成一致，准备签约。', 'positive', '准备正式合同，安排签约仪式', '2023-12-01', '9', 1, '谈判成功，项目即将启动', 1, NOW(), 1, NOW()),

-- 商机7的跟进记录 (在线教育平台) - 失败案例的跟进过程
(13, 7, 'social_media', '2024-05-10 15:20:00', 15, '王校长', '通过LinkedIn联系到客户，了解在线教育平台开发需求。客户有明确的功能要求和预算限制。', 'interested', '准备在线教育解决方案', '2024-05-15', NULL, 2, '社交媒体获客，客户需求明确', 1, NOW(), 1, NOW()),
(14, 7, 'quotation', '2024-05-15 11:00:00', 45, '王校长', '发送详细报价单，价格96万。客户反馈价格偏高，希望能够降低成本。', 'price_concern', '重新评估项目范围，准备优化方案', '2024-05-22', '10', 2, '价格是主要障碍，需要优化方案', 1, NOW(), 1, NOW()),
(15, 7, 'phone', '2024-05-22 09:45:00', 60, '王校长', '电话沟通降价可能性，解释成本构成。客户坚持预算有限，最终选择了竞争对手的低价方案。', 'rejected', '项目失败，总结经验教训', NULL, NULL, 2, '价格竞争失败，需要优化成本控制', 1, NOW(), 1, NOW()),

-- 商机12的跟进记录 (数据分析平台续约) - 续约项目
(16, 12, 'visit', '2024-07-01 10:00:00', 90, '张科长', '拜访政府数据中心，讨论现有平台续约事宜。客户对当前服务满意，希望在续约中增加AI分析功能。', 'positive', '准备续约方案，增加AI功能模块', '2024-07-10', NULL, 1, '续约谈判开局良好，关系稳定', 1, NOW(), 1, NOW()),
(17, 12, 'proposal', '2024-07-10 14:30:00', 60, '张科长', '提交续约技术方案，新增机器学习算法和智能报表功能。价格相比新签有一定优惠。', 'considering', '等待客户内部审批，准备最终谈判', '2024-07-21', '11', 1, '续约方案已提交，等待审批结果', 1, NOW(), 1, NOW()),

-- 更多跟进记录展示不同的跟进方式和结果
(18, 4, 'wechat', '2024-07-12 20:30:00', 30, '黄经理', '微信沟通合作细节，了解孵化器内创业团队的具体需求。大部分是移动应用开发项目。', 'interested', '制定针对创业团队的服务方案', '2024-07-25', NULL, 3, '微信沟通便利，关系融洽', 1, NOW(), 1, NOW()),
(19, 11, 'email', '2024-07-10 08:45:00', 20, '周八', '发送CRM系统功能介绍邮件，重点说明与现有ERP系统的集成方案。', 'need_more_info', '提供详细的集成技术文档', '2024-07-19', '12', 2, '客户需要了解系统集成的技术细节', 1, NOW(), 1, NOW()),
(20, 9, 'phone', '2024-06-30 16:00:00', 40, '刘厂长', '电话了解制造业客户预算审批进展，得知公司内部决策流程较长，项目可能延期到明年。', 'postponed', '保持定期联系，关注项目进展', '2024-09-01', NULL, 1, '客户决策周期长，需要耐心跟进', 1, NOW(), 1, NOW());
-- =====================================================================================================================
-- 1. 业务伙伴表 (partner) - 详细示例数据
-- 涵盖我方公司、供应商、客户、外包个人、政府机构、停用客户等多种角色和状态
-- =====================================================================================================================
DELETE FROM `partner`;
INSERT INTO `partner` (`id`, `partner_name`, `partner_code`, `is_our_company`, `partner_type`, `legal_representative`, `contact_person`, `contact_phone`, `contact_email`, `address`, `certificate_type`, `certificate_number`, `tax_number`, `bank_name`, `bank_account`, `status`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES
(1, '我司信息技术有限公司', 'TSZ-TECH', 1, 'company', '张三', '李四', '***********', '<EMAIL>', '广东省深圳市南山区科技园路1号', 'unified_credit', '91440300MA5G1B2C3D', '91440300MA5G1B2C3D', '招商银行深圳高新园支行', '***************', 'active', '我方公司主体', 1, NOW(), 1, NOW(), 0),
(2, '上海优品供应商', 'SUP-SH-001', 0, 'company', '王五', '赵六', '***********', '<EMAIL>', '上海市浦东新区世纪大道2001号', 'unified_credit', '91310115MA1K1B2G8D', '91310115MA1K1B2G8D', '中国建设银行上海分行', '6227000012345678901', 'active', '长期合作的硬件供应商', 1, NOW(), 1, NOW(), 0),
(3, '北京卓越客户集团', 'CUS-BJ-001', 0, 'company', '孙七', '周八', '***********', '<EMAIL>', '北京市朝阳区建国路88号SOHO现代城', 'unified_credit', '91110105MA01K1B2H9E', '91110105MA01K1B2H9E', '中国工商银行北京朝阳支行', '0200003409008888888', 'active', '年度战略合作客户', 1, NOW(), 1, NOW(), 0),
(4, '个人设计师-吴九', 'PER-WJ-001', 0, 'personal', '吴九', '吴九', '13600136000', '<EMAIL>', '浙江省杭州市西湖区文一西路99号', 'id_card', '330106199001011234', NULL, '支付宝', '<EMAIL>', 'active', '外包UI/UX设计师', 1, NOW(), 1, NOW(), 0),
(5, '深圳智慧建筑工程有限公司', 'CONS-SZ-001', 0, 'company', '钱十', '郑十一', '13500135000', '<EMAIL>', '广东省深圳市宝安区新安街道', 'unified_credit', '91440300MA5H1B2C4E', '91440300MA5H1B2C4E', '平安银行深圳分行', '11012345678901', 'active', '建筑工程承包商', 1, NOW(), 1, NOW(), 0),
(6, '某某劳务派遣公司', 'LAB-SZ-001', 0, 'company', '冯十二', '陈十三', '18600186000', '<EMAIL>', '广东省深圳市龙华区民治大道', 'unified_credit', '91440300MA5J1B2C5F', '91440300MA5J1B2C5F', '中国农业银行深圳龙华支行', '6228480401234567890', 'active', '提供技术人员外包服务', 1, NOW(), 1, NOW(), 0),
(7, 'XX市政府数据中心', 'GOV-XX-001', 0, 'government', '赵局长', '张科长', '0755-88886666', '<EMAIL>', 'XX市XX区市民中心A座', 'unified_credit', '11440300MB1A1B2C6G', '11440300MB1A1B2C6G', '中国银行XX市分行', '777857987654', 'active', '政府合作项目单位', 1, NOW(), 1, NOW(), 0),
(8, '创业孵化器（合作方）', 'PART-CY-001', 0, 'company', '刘总', '黄经理', '18800188000', '<EMAIL>', '江苏省苏州市工业园区星湖街', 'unified_credit', '91320594MA1X1B2C7H', '91320594MA1X1B2C7H', '江苏银行苏州分行', '8888123456789012', 'active', '战略合作伙伴', 1, NOW(), 1, NOW(), 0),
(9, '已停用供应商', 'SUP-OLD-001', 0, 'company', '旧法人', '旧联系人', '13000000000', '<EMAIL>', '已注销地址', 'unified_credit', '91440101MA5K1B2C8I', '91440101MA5K1B2C8I', '已注销银行', '1234567890', 'inactive', '因服务质量问题不再合作', 1, NOW(), 1, NOW(), 0),
(10, '房东张阿姨', 'PER-ZA-001', 0, 'personal', '张爱华', '张爱华', '13100131000', '<EMAIL>', '广东省深圳市南山区XX小区', 'id_card', '******************', NULL, '微信支付', 'zhangayi_wx', 'active', '办公室租赁房东', 1, NOW(), 1, NOW(), 0);

-- =====================================================================================================================
-- 2. 合同主表 (contract) - 详细示例数据
-- 涵盖所有合同状态 (draft-草稿, pending-待签署, active-已生效, completed-已完成, terminated-已终止, cancelled-已作废) 和类型
-- =====================================================================================================================
DELETE FROM `contract`;
INSERT INTO `contract` (`id`, `contract_no`, `contract_name`, `contract_type`, `contract_category`, `contract_amount`, `opportunity_id`, `signing_date`, `effective_date`, `expiry_date`, `contract_status`, `payment_method`, `signing_location`, `responsible_user_id`, `dept_id`, `remark`, `create_by`, `create_time`, `update_by`, `update_time`, `is_deleted`) VALUES
-- Active Contracts
(1, 'CON-PUR-2024-001', '年度硬件设备采购合同', 'purchase', 'external', 150000.00, 1, '2024-01-10', '2024-01-15', '2025-01-14', 'active', 'installment', '深圳市南山区', 1, 2, '向上海优品供应商采购服务器和网络设备', 1, NOW(), 1, NOW(), 0),
(2, 'CON-LEA-2024-001', '总部办公室租赁合同', 'lease', 'external', 360000.00, NULL, '2024-01-05', '2024-01-05', '2027-01-04', 'active', 'installment', '房东家中', 1, 1, '与房东张阿姨签订的三年租赁合同，押二付一', 1, NOW(), 1, NOW(), 0),
-- Completed Contracts
(3, 'CON-SAL-2023-005', '智慧城市项目一期软件销售合同', 'sales', 'external', 1200000.00, 2, '2023-03-15', '2023-03-20', '2024-03-19', 'completed', 'bank_transfer', '北京客户公司会议室', 2, 2, '向北京卓越客户集团销售智慧城市管理平台', 1, NOW(), 1, NOW(), 0),
(4, 'CON-SER-2024-002', '春节活动官网设计服务合同', 'service', 'external', 35000.00, 3, '2024-01-20', '2024-01-20', '2024-02-28', 'completed', 'bank_transfer', '线上签署', 3, 3, '与设计师吴九合作完成活动页面', 1, NOW(), 1, NOW(), 0),
-- Terminated Contract
(5, 'CON-COOP-2023-010', '关于XX项目的合作协议', 'cooperation', 'external', 500000.00, NULL, '2023-08-01', '2023-08-01', '2024-07-31', 'terminated', 'bank_transfer', '苏州创业孵化器', 4, 4, '因市场环境变化，双方协商提前终止合作', 1, NOW(), 1, NOW(), 0),
-- Cancelled Contract
(6, 'CON-PUR-2024-008', '备用服务器采购意向合同', 'purchase', 'external', 80000.00, NULL, '2024-05-10', '2024-05-15', '2024-08-14', 'cancelled', 'bank_transfer', '我司会议室', 1, 2, '供应商无法满足技术要求，合同作废', 1, NOW(), 1, NOW(), 0),
-- Pending Signature Contract
(7, 'CON-CONS-2024-003', '数据中心机房建设工程合同', 'construction', 'external', 2500000.00, 4, '2024-06-20', '2024-07-01', '2025-06-30', 'pending', 'installment', '深圳智慧建筑公司', 5, 5, '与深圳智慧建筑的合同，等待签署', 1, NOW(), 1, NOW(), 0),
-- Draft Contract
(8, 'CON-LAB-2024-004', '2024下半年测试人员外包合同', 'labor', 'external', 180000.00, NULL, NULL, NULL, NULL, 'draft', 'installment', '待定', 3, 3, '与某某劳务派遣公司的合同草稿', 1, NOW(), 1, NOW(), 0),
-- More Active Contracts
(9, 'CON-GOV-2024-001', '政务数据共享平台开发合同', 'service', 'external', 850000.00, 5, '2024-04-10', '2024-04-15', '2025-04-14', 'active', 'installment', 'XX市政府数据中心', 2, 2, '为XX市政府数据中心提供技术开发服务', 1, NOW(), 1, NOW(), 0),
(10, 'CON-SUP-2024-001', '框架协议-补充协议01', 'other', 'supplement', 0.00, NULL, '2024-02-01', '2024-02-01', '2025-01-31', 'active', 'other', '线上签署', 1, 2, '对CON-PUR-2024-001的补充，不涉及金额变更', 1, NOW(), 1, NOW(), 0);

-- =====================================================================================================================
-- 3. 合同伙伴关联表 (contract_partner_relation) - 详细示例数据
-- 包含两方、三方、担保方等多种关系
-- =====================================================================================================================
DELETE FROM `contract_partner_relation`;
-- 合同1: 我司 (采购方) vs 上海优品供应商 (供应商)
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(1, 1, 'party_a', 'purchaser', '李四', '采购总监', 1),
(1, 2, 'party_b', 'supplier', '赵六', '销售经理', 2);
-- 合同2: 我司 (承租方) vs 房东张阿姨 (出租方)
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(2, 1, 'party_b', 'lessee', '李四', '行政经理', 2),
(2, 10, 'party_a', 'lessor', '张爱华', '业主', 1);
-- 合同3: 我司 (销售方) vs 北京卓越客户 (采购方)
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(3, 1, 'party_a', 'supplier', '张三', 'CEO', 1),
(3, 3, 'party_b', 'purchaser', '孙七', 'CTO', 2);
-- 合同4: 我司 (委托方) vs 个人设计师 (服务方)
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(4, 1, 'party_a', 'client', '李四', '市场部经理', 1),
(4, 4, 'party_b', 'supplier', '吴九', '设计师', 2);
-- 合同5: 我司 (合作方A) vs 创业孵化器 (合作方B)
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(5, 1, 'party_a', 'business_partner', '张三', 'CEO', 1),
(5, 8, 'party_b', 'business_partner', '刘总', '创始人', 2);
-- 合同7: 我司 (发包方), 智慧建筑 (承包方), XX银行 (担保方) - 三方合同示例
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(7, 1, 'party_a', 'client', '张三', 'CEO', 1),
(7, 5, 'party_b', 'contractor', '钱十', '总经理', 2),
(7, 7, 'guarantor', 'guarantor', '赵局长', '负责人', 3);
-- 合同9: 我司 (服务方) vs XX市政府 (客户)
INSERT INTO `contract_partner_relation` (`contract_id`, `partner_id`, `partner_role`, `partner_role_desc`, `signing_person`, `signing_person_title`, `sort`) VALUES
(9, 1, 'party_b', 'supplier', '张三', '项目总监', 2),
(9, 7, 'party_a', 'client', '张科长', '科长', 1);

-- =====================================================================================================================
-- 4. 合同付款记录表 (contract_payment) - 详细示例数据
-- 涵盖所有付款状态 (pending, paid, partial, overdue, cancelled) 和类型
-- =====================================================================================================================
DELETE FROM `contract_payment`;
-- 合同1 (硬件采购) - 分期付款，有已付和待付
INSERT INTO `contract_payment` (`contract_id`, `payment_no`, `payment_type`, `payment_method`, `payer_partner_id`, `payee_partner_id`, `planned_amount`, `actual_amount`, `planned_date`, `actual_date`, `payment_status`, `invoice_status`, `invoice_no`, `remark`) VALUES
(1, 'PAY-********-001', 'advance', 'bank_transfer', 1, 2, 50000.00, 50000.00, '2024-01-20', '2024-01-22', 'paid', 'received', 'INV-SH-********', '首付款，已支付'),
(1, 'PAY-********-001', 'progress', 'bank_transfer', 1, 2, 50000.00, 50000.00, '2024-07-20', '2024-07-21', 'paid', 'received', 'INV-SH-********', '中期进度款，已支付'),
(1, 'PAY-********-001', 'final', 'bank_transfer', 1, 2, 50000.00, NULL, '2025-01-20', NULL, 'pending', 'not_issued', NULL, '尾款，待支付');

-- 合同3 (软件销售) - 全部完成
INSERT INTO `contract_payment` (`contract_id`, `payment_no`, `payment_type`, `payment_method`, `payer_partner_id`, `payee_partner_id`, `planned_amount`, `actual_amount`, `planned_date`, `actual_date`, `payment_status`, `invoice_status`, `invoice_no`, `remark`) VALUES
(3, 'PAY-********-001', 'advance', 'bank_transfer', 3, 1, 600000.00, 600000.00, '2023-03-25', '2023-03-26', 'paid', 'issued', 'INV-BJ-********', '首付款'),
(3, 'PAY-********-001', 'final', 'bank_transfer', 3, 1, 600000.00, 600000.00, '2024-03-25', '2024-03-25', 'paid', 'issued', 'INV-BJ-********', '尾款');

-- 合同9 (政府项目) - 包含 overdue 和 partial 状态
INSERT INTO `contract_payment` (`contract_id`, `payment_no`, `payment_type`, `payment_method`, `payer_partner_id`, `payee_partner_id`, `planned_amount`, `actual_amount`, `planned_date`, `actual_date`, `payment_status`, `invoice_status`, `invoice_no`, `remark`) VALUES
(9, 'PAY-********-001', 'deposit', 'bank_transfer', 1, 7, 85000.00, 85000.00, '2024-04-20', '2024-04-21', 'paid', 'received', 'INV-GOV-********', '履约保证金，已支付'),
(9, 'PAY-********-001', 'progress', 'bank_transfer', 7, 1, 300000.00, NULL, '2024-06-30', NULL, 'overdue', 'issued', 'INV-TSZ-********', '第一笔进度款，客户审批流程延迟，已逾期'),
(9, 'PAY-********-001', 'progress', 'bank_transfer', 7, 1, 300000.00, 100000.00, '2024-09-30', '2024-10-08', 'partial', 'issued', 'INV-TSZ-********', '第二笔进度款，客户申请部分支付'),
(9, 'PAY-********-001', 'final', 'bank_transfer', 7, 1, 250000.00, NULL, '2025-04-10', NULL, 'pending', 'not_issued', NULL, '项目尾款，待支付');

-- 合同5 (终止的合作) - 付款被取消
INSERT INTO `contract_payment` (`contract_id`, `payment_no`, `payment_type`, `payment_method`, `payer_partner_id`, `payee_partner_id`, `planned_amount`, `actual_amount`, `planned_date`, `actual_date`, `payment_status`, `invoice_status`, `invoice_no`, `remark`) VALUES
(5, 'PAY-********-001', 'advance', 'bank_transfer', 1, 8, 250000.00, 250000.00, '2023-08-10', '2023-08-11', 'paid', 'received', 'INV-CY-********', '合作启动资金，已支付'),
(5, 'PAY-********-001', 'progress', 'bank_transfer', 1, 8, 250000.00, NULL, '2024-02-10', NULL, 'cancelled', 'not_issued', NULL, '因合同终止，此笔付款已取消');