package com.hntsz.boot.modules.attachment.model.query;

import com.hntsz.boot.common.base.BasePageQuery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "附件查询对象")
public class AttachmentQuery extends BasePageQuery {

    /**
     * 文件名
     */
    @Schema(description = "文件名")
    private String fileName;

    /**
     * 文件类型(IMAGE: 图片, VIDEO: 视频, DOCUMENT: 文档, AUDIO: 音频, OTHER: 其他)
     */
    @Schema(description = "文件类型(IMAGE: 图片, VIDEO: 视频, DOCUMENT: 文档, AUDIO: 音频, OTHER: 其他)")
    private String fileType;

} 