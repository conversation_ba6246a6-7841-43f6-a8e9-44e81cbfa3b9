<template>
  <div class="app-container">
    <!-- 商机信息 -->
    <el-card shadow="never" class="mb-4">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">商机信息</span>
          <el-button @click="goBack">
            <el-icon><ArrowLeft /></el-icon>
            返回商机列表
          </el-button>
        </div>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="商机编号">{{ opportunityInfo.opportunityCode }}</el-descriptions-item>
        <el-descriptions-item label="商机名称">{{ opportunityInfo.opportunityName }}</el-descriptions-item>
        <el-descriptions-item label="预计金额">
          <span v-if="opportunityInfo.estimatedAmount">
            ¥{{ opportunityInfo.estimatedAmount.toLocaleString() }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="商机阶段">
          <el-tag :type="getStageTagType(opportunityInfo.opportunityStage)" effect="light">
            {{ getStageLabel(opportunityInfo.opportunityStage) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="商机状态">
          <el-tag :type="getStatusTagType(opportunityInfo.opportunityStatus)" effect="light">
            {{ getStatusLabel(opportunityInfo.opportunityStatus) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="负责人">{{ opportunityInfo.responsibleUserName }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 跟进记录列表 -->
    <el-card shadow="never">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">跟进记录</span>
          <el-button type="primary" @click="openDrawer()">
            <el-icon><Plus /></el-icon>
            新增跟进记录
          </el-button>
        </div>
      </template>


      <!-- 数据表格 -->
      <el-table v-loading="loading" :data="pageData" border>
        <el-table-column label="跟进时间" prop="followDate" width="180" />
        <el-table-column label="跟进类型" prop="followTypeLabel" width="120" />
        <el-table-column label="联系人" prop="contactPerson" width="120" />
        <el-table-column label="跟进内容" prop="followContent" min-width="200" show-overflow-tooltip />
        <el-table-column label="跟进结果" width="120" align="center">
          <template #default="scope">
            <el-tag :type="getResultTagType(scope.row.followResult)" effect="light">
              {{ scope.row.followResultLabel || scope.row.followResult }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="下次跟进" prop="nextFollowDate" width="120" />
        <el-table-column label="跟进人" prop="followUserName" width="100" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="openDrawer(scope.row.id)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button link type="primary" size="small" @click="handleDetail(scope.row.id)">
              <el-icon><View /></el-icon>
              详情
            </el-button>
            <el-button link type="danger" size="small" @click="handleDelete(scope.row.id)">
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="handleQuery"
      />
    </el-card>

    <!-- 跟进记录表单抽屉 -->
    <el-drawer
      :title="drawer.title"
      v-model="drawer.visible"
      direction="rtl"
      size="600px"
      @close="closeDrawer"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="跟进时间" prop="followDate">
              <el-date-picker
                v-model="formData.followDate"
                type="datetime"
                placeholder="选择跟进时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跟进类型" prop="followType">
              <el-select v-model="formData.followType" placeholder="请选择跟进类型" style="width: 100%">
                <el-option
                  v-for="item in followTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input v-model="formData.contactPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="跟进时长" prop="followDuration">
              <el-input-number
                v-model="formData.followDuration"
                placeholder="请输入跟进时长(分钟)"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="跟进内容" prop="followContent">
          <el-input
            v-model="formData.followContent"
            type="textarea"
            :rows="4"
            placeholder="请输入跟进内容"
          />
        </el-form-item>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="跟进结果" prop="followResult">
              <el-select v-model="formData.followResult" placeholder="请选择跟进结果" style="width: 100%">
                <el-option
                  v-for="item in followResultOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="下次跟进" prop="nextFollowDate">
              <el-date-picker
                v-model="formData.nextFollowDate"
                type="date"
                placeholder="选择下次跟进日期"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="drawer-footer">
          <el-button @click="closeDrawer">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 详情抽屉 -->
    <FollowDetailDrawer
      v-model="detailDrawer.visible"
      :follow-id="detailDrawer.followId"
      @edit="handleEditFromDetail"
    />
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  Plus,
  Delete,
  Edit,
  View,
  ArrowLeft,
} from "@element-plus/icons-vue";
import { useDictStore } from "@/store/modules/dict.store";
import OpportunityFollowAPI, { OpportunityFollowForm } from "@/api/opportunity/follow.api";
import OpportunityAPI from "@/api/opportunity/opportunity.api";
import FollowDetailDrawer from "./components/FollowDetailDrawer.vue";

defineOptions({
  name: "OpportunityFollow",
  inheritAttrs: false,
});

const route = useRoute();
const router = useRouter();
const dictStore = useDictStore();

const dataFormRef = ref(ElForm);
const loading = ref(false);
const total = ref(0);

const opportunityId = ref<string>(route.params.opportunityId as string);
const opportunityInfo = ref<any>({});

// 字典选项
const followTypeOptions = ref<OptionType[]>([]);
const followResultOptions = ref<OptionType[]>([]);

const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  opportunityId: Number(opportunityId.value),
});

const pageData = ref<any[]>([]);

const drawer = reactive({
  title: "",
  visible: false,
});

const detailDrawer = reactive({
  visible: false,
  followId: undefined as number | undefined,
});

const formData = reactive<OpportunityFollowForm>({
  opportunityId: Number(opportunityId.value),
  followDate: "",
  followType: "",
  contactPerson: "",
  followContent: "",
  followResult: "",
  nextFollowDate: "",
  remark: "",
});

const rules = reactive({
  followDate: [{ required: true, message: "请选择跟进时间", trigger: "blur" }],
  followType: [{ required: true, message: "请选择跟进类型", trigger: "change" }],
  followContent: [{ required: true, message: "请输入跟进内容", trigger: "blur" }],
  followResult: [{ required: true, message: "请选择跟进结果", trigger: "change" }],
});

// 标签类型映射
function getStageTagType(stage: string): "success" | "warning" | "info" | "danger" {
  const stageMap: Record<string, "success" | "warning" | "info" | "danger"> = {
    initial: "info",
    qualification: "warning",
    proposal: "info",
    negotiation: "warning",
    closed_won: "success",
    closed_lost: "danger",
  };
  return stageMap[stage] || "info";
}

function getStatusTagType(status: string): "success" | "warning" | "info" | "danger" {
  const statusMap: Record<string, "success" | "warning" | "info" | "danger"> = {
    active: "success",
    won: "success",
    lost: "danger",
    cancelled: "warning",
    archived: "info",
  };
  return statusMap[status] || "info";
}

function getResultTagType(result: string): "success" | "warning" | "info" | "danger" | "primary" {
  const resultMap: Record<string, "success" | "warning" | "info" | "danger" | "primary"> = {
    positive: "success",
    interested: "primary",
    considering: "info",
    need_more_info: "warning",
    price_concern: "danger",
    no_response: "info",
    rejected: "danger",
    postponed: "warning",
    other: "info",
  };
  return resultMap[result] || "info";
}

function getStageLabel(stage: string): string {
  const stageMap: Record<string, string> = {
    initial: "初步接触",
    qualification: "需求确认",
    proposal: "方案提交",
    negotiation: "商务谈判",
    closed_won: "成功签约",
    closed_lost: "失败结束",
  };
  return stageMap[stage] || stage;
}

function getStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    active: "进行中",
    won: "已成交",
    lost: "已失败",
    cancelled: "已取消",
    archived: "已归档",
  };
  return statusMap[status] || status;
}



function goBack() {
  router.push("/opportunity/list");
}

function handleQuery() {
  loading.value = true;
  OpportunityFollowAPI.getPage(queryParams)
    .then((response: any) => {
      console.log('跟进记录分页API响应:', response);
      // 处理不同的响应结构
      if (response.data && response.data.list) {
        pageData.value = response.data.list;
        total.value = response.data.total;
      } else if (response.list) {
        pageData.value = response.list;
        total.value = response.total;
      } else if (Array.isArray(response)) {
        pageData.value = response;
        total.value = response.length;
      } else {
        pageData.value = [];
        total.value = 0;
      }
      console.log('跟进记录数据:', pageData.value);
    })
    .catch((error) => {
      console.error('获取跟进记录失败:', error);
      pageData.value = [];
      total.value = 0;
      ElMessage.error('获取跟进记录失败');
    })
    .finally(() => {
      loading.value = false;
    });
}


function openDrawer(id?: string) {
  drawer.title = id ? "编辑跟进记录" : "新增跟进记录";
  drawer.visible = true;

  if (id) {
    // 加载跟进记录详情
    OpportunityFollowAPI.getFormData(Number(id))
      .then((response: any) => {
        console.log('跟进记录详情API响应:', response);
        // 处理不同的响应结构
        let data = response;
        if (response.data) {
          data = response.data;
        }
        Object.assign(formData, data);
        console.log('设置表单数据:', formData);
      })
      .catch((error) => {
        console.error('获取跟进记录详情失败:', error);
        ElMessage.error('获取详情失败');
        closeDrawer();
      });
  } else {
    resetFormData();
  }
}

function closeDrawer() {
  drawer.visible = false;
  resetFormData();
}

function resetFormData() {
  Object.assign(formData, {
    id: undefined,
    opportunityId: Number(opportunityId.value),
    followDate: "",
    followType: "",
    contactPerson: "",
    followDuration: undefined,
    followContent: "",
    followResult: "",
    nextFollowDate: "",
    remark: "",
  });
}

function handleSubmit() {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      const id = formData.id;
      const submitData = { ...formData };

      console.log('提交跟进记录数据:', submitData);

      if (id) {
        // 编辑模式
        OpportunityFollowAPI.update(id, submitData)
          .then(() => {
            ElMessage.success("修改成功");
            closeDrawer();
            handleQuery();
          })
          .catch((error) => {
            console.error('修改跟进记录失败:', error);
            ElMessage.error('修改失败');
          });
      } else {
        // 新增模式
        OpportunityFollowAPI.create(submitData)
          .then(() => {
            ElMessage.success("新增成功");
            closeDrawer();
            handleQuery();
          })
          .catch((error) => {
            console.error('新增跟进记录失败:', error);
            ElMessage.error('新增失败');
          });
      }
    }
  });
}

function handleDelete(id: string) {
  ElMessageBox.confirm("确认删除该跟进记录?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(() => {
    OpportunityFollowAPI.delete(id)
      .then(() => {
        ElMessage.success("删除成功");
        handleQuery();
      })
      .catch((error) => {
        console.error('删除跟进记录失败:', error);
        ElMessage.error('删除失败');
      });
  });
}

function handleDetail(id: string) {
  detailDrawer.followId = Number(id);
  detailDrawer.visible = true;
}

function handleEditFromDetail(id: number) {
  // 从详情页面跳转到编辑
  openDrawer(String(id));
}

async function loadOpportunityInfo() {
  try {
    const response: any = await OpportunityAPI.getFormData(Number(opportunityId.value));
    console.log('商机信息API响应:', response);

    // 处理不同的响应结构
    let data = response;
    if (response.data) {
      data = response.data;
    }

    opportunityInfo.value = data;
    console.log('商机信息:', opportunityInfo.value);
  } catch (error) {
    console.error('获取商机信息失败:', error);
    ElMessage.error("获取商机信息失败");
    goBack();
  }
}

async function initDictData() {
  // 加载跟进类型字典
  await dictStore.loadDictItems("follow_type");
  followTypeOptions.value = dictStore.getDictItems("follow_type");

  // 加载跟进结果字典
  await dictStore.loadDictItems("follow_result");
  followResultOptions.value = dictStore.getDictItems("follow_result");
}

onMounted(async () => {
  await loadOpportunityInfo();
  await initDictData();
  handleQuery();
});
</script>

<style scoped>
.drawer-footer {
  text-align: right;
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}
</style>
