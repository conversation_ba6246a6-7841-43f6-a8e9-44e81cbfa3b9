// 商机相关类型定义
import type { PageQuery } from './global'

// 商机查询参数
export interface OpportunityQuery extends PageQuery {
  opportunityCode?: string
  opportunityName?: string
  opportunityType?: string
  opportunitySource?: string
  partnerId?: number
  opportunityStage?: string
  opportunityStatus?: string
  priority?: string
  responsibleUserId?: number
  deptId?: number
  minEstimatedAmount?: number
  maxEstimatedAmount?: number
  minWinProbability?: number
  maxWinProbability?: number
  estimatedCloseStartDate?: string
  estimatedCloseEndDate?: string
  actualCloseStartDate?: string
  actualCloseEndDate?: string
  createStartTime?: string
  createEndTime?: string
  lostReason?: string
  tags?: string
}

// 商机信息
export interface Opportunity {
  id?: number
  opportunityCode?: string
  opportunityName?: string
  opportunityType?: string
  opportunityTypeLabel?: string
  opportunitySource?: string
  opportunitySourceLabel?: string
  partnerId?: number
  partnerName?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  opportunityStage?: string
  opportunityStageLabel?: string
  winProbability?: number
  estimatedAmount?: number
  estimatedCloseDate?: string
  actualCloseDate?: string
  opportunityStatus?: string
  opportunityStatusLabel?: string
  lostReason?: string
  lostReasonLabel?: string
  priority?: string
  priorityLabel?: string
  productInterest?: string
  requirements?: string
  competitionInfo?: string
  nextAction?: string
  nextFollowDate?: string
  responsibleUserId?: number
  responsibleUserName?: string
  deptId?: number
  deptName?: string
  tags?: string
  remark?: string
  followCount?: number
  lastFollowDate?: string
  createTime?: string
  updateTime?: string
}

// 商机表单
export interface OpportunityForm extends Opportunity {}

// 跟进记录查询参数
export interface FollowQuery extends PageQuery {
  opportunityId?: number
  followType?: string
  followUserId?: number
  followStartDate?: string
  followEndDate?: string
}

// 跟进记录
export interface Follow {
  id?: number
  opportunityId?: number
  followType?: string
  followTypeLabel?: string
  followDate?: string
  followDuration?: number
  contactPerson?: string
  followContent?: string
  followResult?: string
  followResultLabel?: string
  nextAction?: string
  nextFollowDate?: string
  attachmentId?: string
  followUserId?: number
  followUserName?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 跟进记录表单
export interface FollowForm extends Follow {}