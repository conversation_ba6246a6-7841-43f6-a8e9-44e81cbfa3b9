<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="探数者综合管理平台" />
    <meta
      name="keywords"
      content="探数者综合管理平台"
    />
    <title>探数者综合管理平台</title>
  </head>

  <body>
    <div id="app">
      <div class="loading-container">
        <div class="loading-spinner">
          <div class="loading-bar"></div>
          <div class="loading-bar"></div>
          <div class="loading-bar"></div>
        </div>
      </div>
    </div>
  </body>
  <script type="module" src="/src/main.ts"></script>

  <style>
    html,
    body,
    #app {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      gap: 24px;
      align-items: center;
      justify-content: center;
    }

    .loading-spinner {
      display: flex;
      gap: 6px;
      align-items: center;
      justify-content: center;
      height: 40px;
    }

    .loading-bar {
      width: 4px;
      height: 24px;
      background-color: #498cff;
      border-radius: 2px;
      animation: loading-animation 1.2s ease-in-out infinite;
    }

    .loading-bar:nth-child(1) {
      animation-delay: 0s;
    }

    .loading-bar:nth-child(2) {
      animation-delay: 0.2s;
    }

    .loading-bar:nth-child(3) {
      animation-delay: 0.4s;
    }

    @keyframes loading-animation {
      0% {
        opacity: 0.3;
        transform: scaleY(0.5);
      }

      50% {
        opacity: 1;
        transform: scaleY(1.2);
      }

      100% {
        opacity: 0.3;
        transform: scaleY(0.5);
      }
    }
  </style>
</html>
