// 合同相关类型定义
import type { PageQuery } from './global'

// 合同查询参数
export interface ContractQuery extends PageQuery {
  keywords?: string
  contractType?: string
  contractCategory?: string
  contractStatus?: string
  responsibleUserId?: string
  deptId?: string
  signingDateRange?: [string, string]
  effectiveDateRange?: [string, string]
  expiryDateRange?: [string, string]
  contractAmountRange?: [number, number]
  partnerId?: string
  opportunityId?: string
}

// 合同信息
export interface Contract {
  id?: string
  contractNo?: string
  contractName?: string
  contractType?: string
  contractTypeLabel?: string
  contractCategory?: string
  contractCategoryLabel?: string
  contractAmount?: number
  opportunityId?: string
  opportunityName?: string
  signingDate?: string
  effectiveDate?: string
  expiryDate?: string
  contractStatus?: string
  paymentMethod?: string
  paymentMethodLabel?: string
  signingLocation?: string
  responsibleUserId?: string
  responsibleUserName?: string
  deptId?: string
  deptName?: string
  remark?: string
  parties?: ContractPartner[]
  attachments?: ContractAttachment[]
  totalPaymentAmount?: number
  createTime?: string
  updateTime?: string
}

// 合同表单
export interface ContractForm extends Omit<Contract, 'parties'> {
  parties?: ContractPartnerForm[]
}

// 合同伙伴关联
export interface ContractPartner {
  id?: string
  contractId?: string
  partnerId?: string
  partnerName?: string
  partnerRole?: string
  partnerRoleLabel?: string
  partnerRoleDesc?: string
  partnerRoleDescLabel?: string
  signingPerson?: string
  signingPersonTitle?: string
  signingDate?: string
  sort?: number
  remark?: string
}

// 合同伙伴关联表单
export interface ContractPartnerForm extends ContractPartner {}

// 合同附件
export interface ContractAttachment {
  id?: string
  contractId?: string
  attachmentId?: string
  attachmentName?: string
  attachmentUrl?: string
  sort?: number
}

// 业务伙伴查询参数
export interface PartnerQuery extends PageQuery {
  partnerName?: string
  partnerCode?: string
  partnerType?: string
  contactPerson?: string
  status?: string
}

// 业务伙伴
export interface Partner {
  id?: string
  partnerName?: string
  partnerCode?: string
  isOurCompany?: boolean
  partnerType?: string
  partnerTypeLabel?: string
  legalRepresentative?: string
  contactPerson?: string
  contactPhone?: string
  contactEmail?: string
  address?: string
  certificateType?: string
  certificateTypeLabel?: string
  certificateNumber?: string
  taxNumber?: string
  bankName?: string
  bankAccount?: string
  status?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 业务伙伴表单
export interface PartnerForm extends Partner {}

// 付款记录查询参数
export interface PaymentQuery extends PageQuery {
  contractId?: string
  paymentType?: string
  paymentStatus?: string
  payerPartnerId?: string
  payeePartnerId?: string
  plannedStartDate?: string
  plannedEndDate?: string
  actualStartDate?: string
  actualEndDate?: string
}

// 付款记录
export interface Payment {
  id?: string
  contractId?: string
  paymentNo?: string
  paymentType?: string
  paymentTypeLabel?: string
  paymentMethod?: string
  paymentMethodLabel?: string
  payerPartnerId?: string
  payerPartnerName?: string
  payeePartnerId?: string
  payeePartnerName?: string
  plannedAmount?: number
  actualAmount?: number
  currency?: string
  plannedDate?: string
  actualDate?: string
  paymentStatus?: string
  paymentStatusLabel?: string
  bankName?: string
  bankAccount?: string
  transactionNo?: string
  voucherAttachmentId?: string
  invoiceStatus?: string
  invoiceStatusLabel?: string
  invoiceNo?: string
  invoiceAmount?: number
  invoiceDate?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 付款记录表单
export interface PaymentForm extends Payment {}