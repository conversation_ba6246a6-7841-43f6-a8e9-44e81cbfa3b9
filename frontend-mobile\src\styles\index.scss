// 全局样式文件

// 重置样式
* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8f8f8;
}

#app {
  width: 100%;
  height: 100%;
}

// 通用类
.container {
  padding: 16px;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

// 间距类
.mt-8 {
  margin-top: 8px;
}

.mt-16 {
  margin-top: 16px;
}

.mb-8 {
  margin-bottom: 8px;
}

.mb-16 {
  margin-bottom: 16px;
}

.ml-8 {
  margin-left: 8px;
}

.mr-8 {
  margin-right: 8px;
}

.p-8 {
  padding: 8px;
}

.p-16 {
  padding: 16px;
}

.px-16 {
  padding-left: 16px;
  padding-right: 16px;
}

.py-8 {
  padding-top: 8px;
  padding-bottom: 8px;
}

// 颜色类
.text-primary {
  color: #1989fa;
}

.text-success {
  color: #07c160;
}

.text-warning {
  color: #ff976a;
}

.text-danger {
  color: #ee0a24;
}

.text-gray {
  color: #969799;
}

.text-gray-light {
  color: #c8c9cc;
}

// 字体大小
.text-xs {
  font-size: 12px;
}

.text-sm {
  font-size: 14px;
}

.text-base {
  font-size: 16px;
}

.text-lg {
  font-size: 18px;
}

.text-xl {
  font-size: 20px;
}

// 自定义组件样式
.page {
  min-height: 100vh;
  background-color: #f8f8f8;
  
  &__header {
    background-color: #fff;
    border-bottom: 1px solid #ebedf0;
  }
  
  &__content {
    flex: 1;
    padding: 16px;
  }
  
  &__footer {
    background-color: #fff;
    border-top: 1px solid #ebedf0;
  }
}

.card {
  background-color: #fff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
    }
  }
  
  &__content {
    color: #646566;
    line-height: 1.6;
  }
}

.list-item {
  background-color: #fff;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  
  &:last-child {
    border-bottom: none;
  }
  
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .title {
      font-size: 16px;
      font-weight: 500;
      color: #323233;
    }
    
    .status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 12px;
      
      &--primary {
        background-color: #e6f2ff;
        color: #1989fa;
      }
      
      &--success {
        background-color: #e1f5e1;
        color: #07c160;
      }
      
      &--warning {
        background-color: #fff2e8;
        color: #ff976a;
      }
      
      &--danger {
        background-color: #ffeaea;
        color: #ee0a24;
      }
    }
  }
  
  &__content {
    color: #646566;
    font-size: 14px;
    line-height: 1.5;
  }
  
  &__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #969799;
  }
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #969799;
  
  &__image {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
    opacity: 0.3;
  }
  
  &__text {
    font-size: 14px;
  }
}

// 表单样式
.form-section {
  background-color: #fff;
  margin-bottom: 12px;
  
  &__title {
    padding: 16px;
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    border-bottom: 1px solid #ebedf0;
  }
}

// 详情页样式
.detail {
  &__header {
    background-color: #fff;
    padding: 16px;
    margin-bottom: 12px;
    
    .title {
      font-size: 18px;
      font-weight: 500;
      color: #323233;
      margin-bottom: 8px;
    }
    
    .subtitle {
      font-size: 14px;
      color: #969799;
    }
  }
  
  &__section {
    background-color: #fff;
    margin-bottom: 12px;
    
    .section-title {
      padding: 16px;
      font-size: 16px;
      font-weight: 500;
      color: #323233;
      border-bottom: 1px solid #ebedf0;
    }
    
    .section-content {
      padding: 16px;
    }
  }
  
  &__field {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      width: 80px;
      flex-shrink: 0;
      font-size: 14px;
      color: #646566;
      line-height: 1.6;
    }
    
    .value {
      flex: 1;
      font-size: 14px;
      color: #323233;
      line-height: 1.6;
      word-break: break-all;
    }
  }
}

// 搜索样式
.search-bar {
  background-color: #fff;
  padding: 16px;
  margin-bottom: 12px;
}

// 筛选样式
.filter-bar {
  background-color: #fff;
  padding: 8px 16px;
  border-bottom: 1px solid #ebedf0;
  
  .filter-item {
    display: inline-block;
    margin-right: 12px;
    margin-bottom: 8px;
    padding: 4px 12px;
    background-color: #f7f8fa;
    border-radius: 16px;
    font-size: 12px;
    color: #646566;
    
    &--active {
      background-color: #e6f2ff;
      color: #1989fa;
    }
  }
}

// 固定底部按钮
.fixed-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 12px 16px;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  z-index: 100;
}