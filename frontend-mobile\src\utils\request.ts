import axios, { type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { showFailToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// API响应接口
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json;charset=utf-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 显示加载提示
    if (config.showLoading !== false) {
      showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        duration: 0
      })
    }

    const userStore = useUserStore()
    
    // 添加认证token
    if (userStore.token && config.headers) {
      // 跳过无需认证的接口
      if (config.headers['Authorization'] !== 'no-auth') {
        config.headers['Authorization'] = `Bearer ${userStore.token}`
      } else {
        delete config.headers['Authorization']
      }
    }

    return config
  },
  (error) => {
    closeToast()
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    closeToast()
    
    const { code, message, data } = response.data

    // 请求成功
    if (code === 200 || code === 0) {
      return data
    }

    // token过期或无效
    if (code === 401) {
      const userStore = useUserStore()
      userStore.logout()
      router.push('/login')
      showFailToast('登录已过期，请重新登录')
      return Promise.reject(new Error(message))
    }

    // 其他错误
    showFailToast(message || '请求失败')
    return Promise.reject(new Error(message))
  },
  (error) => {
    closeToast()
    
    let message = '网络错误'
    
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权访问'
          const userStore = useUserStore()
          userStore.logout()
          router.push('/login')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求资源不存在'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        default:
          message = `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message.includes('Network Error')) {
      message = '网络连接异常'
    }

    showFailToast(message)
    return Promise.reject(error)
  }
)

// 扩展AxiosRequestConfig类型
declare module 'axios' {
  interface AxiosRequestConfig {
    showLoading?: boolean
  }
}

export default service