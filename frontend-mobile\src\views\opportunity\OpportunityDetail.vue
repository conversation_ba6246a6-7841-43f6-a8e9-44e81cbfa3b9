<template>
  <div class="opportunity-detail-page">
    <!-- 头部导航 -->
    <van-nav-bar
      :title="opportunity?.opportunityName || '商机详情'"
      left-arrow
      @click-left="goBack"
    >
      <template #right>
        <van-icon name="edit" @click="editOpportunity" />
      </template>
    </van-nav-bar>

    <div v-if="opportunity" class="detail-content">
      <!-- 基本信息卡片 -->
      <div class="detail-card">
        <div class="card-header">
          <div class="title-section">
            <h2 class="opportunity-title">{{ opportunity.opportunityName }}</h2>
            <span :class="['status-badge', `status-${opportunity.opportunityStatus}`]">
              {{ opportunity.opportunityStatusLabel }}
            </span>
          </div>
          <div class="code-section">
            <span class="code">{{ opportunity.opportunityCode }}</span>
            <span class="priority">{{ opportunity.priorityLabel }}</span>
          </div>
        </div>

        <div class="key-metrics">
          <div class="metric-item">
            <div class="metric-value">¥{{ formatMoney(opportunity.estimatedAmount || 0) }}</div>
            <div class="metric-label">预估金额</div>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-item">
            <div class="metric-value">{{ opportunity.winProbability }}%</div>
            <div class="metric-label">成单概率</div>
          </div>
          <div class="metric-divider"></div>
          <div class="metric-item">
            <div class="metric-value">{{ opportunity.followCount || 0 }}</div>
            <div class="metric-label">跟进次数</div>
          </div>
        </div>
      </div>

      <!-- 阶段进度 -->
      <div class="detail-card">
        <div class="section-title">商机阶段</div>
        <div class="stage-progress">
          <div
            v-for="(stage, index) in stages"
            :key="stage.value"
            :class="['stage-item', { 
              'active': stage.value === opportunity.opportunityStage,
              'completed': getStageIndex(opportunity.opportunityStage) > index
            }]"
          >
            <div class="stage-dot"></div>
            <div class="stage-label">{{ stage.label }}</div>
          </div>
        </div>
      </div>

      <!-- 商机信息 -->
      <div class="detail-card">
        <div class="section-title">商机信息</div>
        <div class="info-list">
          <div class="info-item">
            <span class="label">商机类型</span>
            <span class="value">{{ opportunity.opportunityTypeLabel }}</span>
          </div>
          <div class="info-item">
            <span class="label">商机来源</span>
            <span class="value">{{ opportunity.opportunitySourceLabel }}</span>
          </div>
          <div class="info-item">
            <span class="label">预计成交日期</span>
            <span class="value">{{ formatDate(opportunity.estimatedCloseDate) || '未设置' }}</span>
          </div>
          <div class="info-item">
            <span class="label">实际成交日期</span>
            <span class="value">{{ formatDate(opportunity.actualCloseDate) || '未成交' }}</span>
          </div>
          <div class="info-item">
            <span class="label">负责人</span>
            <span class="value">{{ opportunity.responsibleUserName }}</span>
          </div>
          <div class="info-item">
            <span class="label">所属部门</span>
            <span class="value">{{ opportunity.deptName }}</span>
          </div>
        </div>
      </div>

      <!-- 客户信息 -->
      <div class="detail-card">
        <div class="section-title">客户信息</div>
        <div class="info-list">
          <div class="info-item">
            <span class="label">客户名称</span>
            <span class="value">{{ opportunity.partnerName || '未关联' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系人</span>
            <span class="value">{{ opportunity.contactPerson || '未填写' }}</span>
          </div>
          <div class="info-item">
            <span class="label">联系电话</span>
            <span class="value">
              <a v-if="opportunity.contactPhone" :href="`tel:${opportunity.contactPhone}`">
                {{ opportunity.contactPhone }}
              </a>
              <span v-else>未填写</span>
            </span>
          </div>
          <div class="info-item">
            <span class="label">联系邮箱</span>
            <span class="value">
              <a v-if="opportunity.contactEmail" :href="`mailto:${opportunity.contactEmail}`">
                {{ opportunity.contactEmail }}
              </a>
              <span v-else>未填写</span>
            </span>
          </div>
        </div>
      </div>

      <!-- 商机描述 -->
      <div class="detail-card">
        <div class="section-title">商机描述</div>
        <div class="info-list">
          <div class="info-item">
            <span class="label">产品/服务兴趣</span>
            <span class="value">{{ opportunity.productInterest || '未填写' }}</span>
          </div>
          <div class="info-item">
            <span class="label">客户需求</span>
            <span class="value">{{ opportunity.requirements || '未填写' }}</span>
          </div>
          <div class="info-item">
            <span class="label">竞争对手信息</span>
            <span class="value">{{ opportunity.competitionInfo || '未填写' }}</span>
          </div>
          <div class="info-item">
            <span class="label">下一步行动</span>
            <span class="value">{{ opportunity.nextAction || '未填写' }}</span>
          </div>
          <div class="info-item">
            <span class="label">下次跟进日期</span>
            <span class="value">{{ formatDate(opportunity.nextFollowDate) || '未设置' }}</span>
          </div>
          <div class="info-item">
            <span class="label">备注</span>
            <span class="value">{{ opportunity.remark || '无' }}</span>
          </div>
        </div>
      </div>

      <!-- 最近跟进 -->
      <div class="detail-card">
        <div class="section-title">
          最近跟进
          <van-button type="primary" size="mini" @click="goToFollowList">
            查看全部
          </van-button>
        </div>
        <div v-if="recentFollows.length > 0" class="follow-list">
          <div
            v-for="follow in recentFollows"
            :key="follow.id"
            class="follow-item"
          >
            <div class="follow-header">
              <span class="follow-type">{{ follow.followTypeLabel }}</span>
              <span class="follow-time">{{ formatDateTime(follow.followDate) }}</span>
            </div>
            <div class="follow-content">{{ follow.followContent }}</div>
            <div class="follow-footer">
              <span class="follow-user">{{ follow.followUserName }}</span>
              <span class="follow-result">{{ follow.followResultLabel }}</span>
            </div>
          </div>
        </div>
        <van-empty v-else description="暂无跟进记录" />
      </div>
    </div>

    <!-- 加载状态 -->
    <van-loading v-else type="spinner" color="#1989fa" class="loading-center">
      加载中...
    </van-loading>

    <!-- 底部操作栏 -->
    <div class="bottom-actions">
      <van-button
        type="primary"
        size="large"
        @click="addFollow"
      >
        新增跟进
      </van-button>
      <van-button
        type="default"
        size="large"
        @click="updateStage"
      >
        更新阶段
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showFailToast, showSuccessToast } from 'vant'
import { opportunityApi, followApi } from '@/api/opportunity'
import type { Opportunity, Follow } from '@/types/opportunity'
import { formatMoney, formatDate, formatDateTime } from '@/utils/format'

const router = useRouter()
const route = useRoute()

// 商机详情
const opportunity = ref<Opportunity | null>(null)
const recentFollows = ref<Follow[]>([])

// 商机阶段配置
const stages = [
  { label: '初步接触', value: 'initial' },
  { label: '有意向', value: 'interested' },
  { label: '方案阶段', value: 'proposal' },
  { label: '谈判阶段', value: 'negotiation' },
  { label: '成交', value: 'closed_won' }
]

// 获取阶段索引
const getStageIndex = (stage: string) => {
  return stages.findIndex(s => s.value === stage)
}

// 加载商机详情
const loadOpportunityDetail = async () => {
  const id = Number(route.params.id)
  if (!id) {
    showFailToast('商机ID无效')
    return
  }

  try {
    opportunity.value = await opportunityApi.getDetail(id)
  } catch (error) {
    console.error('加载商机详情失败:', error)
    showFailToast('加载失败')
  }
}

// 加载最近跟进记录
const loadRecentFollows = async () => {
  const id = Number(route.params.id)
  if (!id) return

  try {
    const result = await followApi.getPage({
      opportunityId: id,
      pageNum: 1,
      pageSize: 3
    })
    recentFollows.value = result.records
  } catch (error) {
    console.error('加载跟进记录失败:', error)
  }
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 编辑商机
const editOpportunity = () => {
  router.push(`/opportunity/${route.params.id}/edit`)
}

// 跳转到跟进列表
const goToFollowList = () => {
  router.push(`/opportunity/${route.params.id}/follow`)
}

// 新增跟进
const addFollow = () => {
  router.push(`/opportunity/${route.params.id}/follow/create`)
}

// 更新阶段
const updateStage = () => {
  // 这里可以弹出阶段选择弹窗
  showSuccessToast('阶段更新功能开发中...')
}

// 组件挂载
onMounted(() => {
  loadOpportunityDetail()
  loadRecentFollows()
})
</script>

<style scoped lang="scss">
.opportunity-detail-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 80px;
}

.detail-content {
  padding: 8px 16px;
}

.detail-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  
  .card-header {
    margin-bottom: 16px;
    
    .title-section {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 8px;
      
      .opportunity-title {
        font-size: 18px;
        font-weight: 600;
        color: #323233;
        margin: 0;
        flex: 1;
        margin-right: 12px;
        line-height: 1.3;
      }
      
      .status-badge {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
        flex-shrink: 0;
        
        &.status-active {
          background: #e6f2ff;
          color: #1989fa;
        }
        
        &.status-won {
          background: #e1f5e1;
          color: #07c160;
        }
        
        &.status-lost {
          background: #ffeaea;
          color: #ee0a24;
        }
      }
    }
    
    .code-section {
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #969799;
      
      .code {
        margin-right: 12px;
      }
      
      .priority {
        padding: 2px 8px;
        background: #f7f8fa;
        border-radius: 8px;
      }
    }
  }
  
  .key-metrics {
    display: flex;
    align-items: center;
    padding: 16px 0;
    
    .metric-item {
      flex: 1;
      text-align: center;
      
      .metric-value {
        font-size: 20px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 4px;
      }
      
      .metric-label {
        font-size: 12px;
        color: #969799;
      }
    }
    
    .metric-divider {
      width: 1px;
      height: 40px;
      background: #ebedf0;
      margin: 0 16px;
    }
  }
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stage-progress {
  display: flex;
  align-items: center;
  overflow-x: auto;
  padding: 8px 0;
  
  .stage-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
    position: relative;
    
    &:not(:last-child)::after {
      content: '';
      position: absolute;
      top: 8px;
      right: -50%;
      width: 100%;
      height: 2px;
      background: #ebedf0;
      z-index: 1;
    }
    
    &.completed::after {
      background: #07c160;
    }
    
    .stage-dot {
      width: 16px;
      height: 16px;
      border-radius: 50%;
      background: #ebedf0;
      margin-bottom: 8px;
      position: relative;
      z-index: 2;
    }
    
    .stage-label {
      font-size: 12px;
      color: #969799;
      text-align: center;
      line-height: 1.2;
    }
    
    &.active {
      .stage-dot {
        background: #1989fa;
      }
      
      .stage-label {
        color: #1989fa;
        font-weight: 500;
      }
    }
    
    &.completed {
      .stage-dot {
        background: #07c160;
      }
      
      .stage-label {
        color: #07c160;
      }
    }
  }
}

.info-list {
  .info-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      width: 100px;
      flex-shrink: 0;
      font-size: 14px;
      color: #646566;
      line-height: 1.6;
    }
    
    .value {
      flex: 1;
      font-size: 14px;
      color: #323233;
      line-height: 1.6;
      word-break: break-all;
      
      a {
        color: #1989fa;
        text-decoration: none;
      }
    }
  }
}

.follow-list {
  .follow-item {
    padding: 12px;
    background: #f7f8fa;
    border-radius: 8px;
    margin-bottom: 8px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .follow-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      
      .follow-type {
        font-size: 12px;
        color: #1989fa;
        background: #e6f2ff;
        padding: 2px 8px;
        border-radius: 8px;
      }
      
      .follow-time {
        font-size: 12px;
        color: #969799;
      }
    }
    
    .follow-content {
      font-size: 14px;
      color: #323233;
      line-height: 1.5;
      margin-bottom: 8px;
    }
    
    .follow-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      
      .follow-user {
        color: #646566;
      }
      
      .follow-result {
        color: #07c160;
      }
    }
  }
}

.loading-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.08);
  display: flex;
  gap: 12px;
  
  .van-button {
    flex: 1;
  }
}
</style>