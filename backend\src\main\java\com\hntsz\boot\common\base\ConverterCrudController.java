package com.hntsz.boot.common.base;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntsz.boot.common.base.BasePageQuery;

/**
 * 通用 CRUD 控制器
 *
 * @param <T> 实体类型
 * @param <V> VO类型
 * @param <CF> Create Form类型
 * @param <UF> Update Form类型
 * @param <M> Mapper 类型
 * @param <C> Converter 类型
 */
public abstract class ConverterCrudController<T, Q extends BasePageQuery, V, D, CF, UF, M extends BaseMapper<T>, C extends ICrudConverter<T, V, D, CF, UF>> 
    extends BaseCrudController<T, Q, V, D, CF, UF, M> {

    protected final C converter;

    protected ConverterCrudController(M baseMapper, C converter) {
        super(baseMapper);
        this.converter = converter;
    }

    /**
     * 将实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    @Override
    protected V convertToVO(T entity) {
        return converter.entityToVo(entity);
    }

    /**
     * 将实体转换为详情
     *
     * @param entity 实体对象
     * @return 详情对象
     */
    @Override
    protected D convertToDetail(T entity) {
        return converter.entityToDetail(entity);
    }

    /**
     * 将Create Form转换为实体
     *
     * @param form Form对象
     * @return 实体对象
     */
    @Override
    protected T convertCfToEntity(CF form) {
        return converter.createFormToEntity(form);
    }

    /**
     * 将Update Form转换为实体
     *
     * @param form Update Form对象
     * @return 实体对象
     */
    @Override
    protected T convertUfToEntity(UF form) {
        return converter.updateFormToEntity(form);
    }
    
    /**
     * 将实体分页转换为VO分页
     *
     * @param entityPage 实体分页
     * @return VO分页
     */
    protected IPage<V> convertToVOPage(IPage<T> entityPage) {
        return entityPage.convert(this::convertToVO);
    }
} 