package com.hntsz.boot.modules.opportunity.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商机线索表单对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机线索表单对象")
public class OpportunityForm {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "商机编码(自动生成)")
    private String opportunityCode;

    @Schema(description = "商机名称")
    @NotBlank(message = "商机名称不能为空")
    @Size(max = 200, message = "商机名称长度不能超过200个字符")
    private String opportunityName;

    @Schema(description = "商机类型")
    @NotBlank(message = "商机类型不能为空")
    private String opportunityType;

    @Schema(description = "商机来源")
    private String opportunitySource;

    @Schema(description = "关联客户ID")
    private Long partnerId;

    @Schema(description = "联系人")
    @Size(max = 100, message = "联系人长度不能超过100个字符")
    private String contactPerson;

    @Schema(description = "联系电话")
    @Pattern(regexp = "^[0-9-]+$", message = "联系电话格式不正确")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    @Email(message = "联系邮箱格式不正确")
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    private String contactEmail;

    @Schema(description = "商机阶段")
    private String opportunityStage;

    @Schema(description = "成单概率(%)")
    @Min(value = 0, message = "成单概率不能小于0")
    @Max(value = 100, message = "成单概率不能大于100")
    private Integer winProbability;

    @Schema(description = "预估金额")
    @DecimalMin(value = "0", message = "预估金额不能小于0")
    private BigDecimal estimatedAmount;

    @Schema(description = "预计成交日期")
    private LocalDate estimatedCloseDate;

    @Schema(description = "实际成交日期")
    private LocalDate actualCloseDate;

    @Schema(description = "商机状态")
    private String opportunityStatus;

    @Schema(description = "失败原因")
    private String lostReason;

    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "感兴趣的产品/服务")
    @Size(max = 500, message = "感兴趣的产品/服务长度不能超过500个字符")
    private String productInterest;

    @Schema(description = "客户需求描述")
    private String requirements;

    @Schema(description = "竞争对手信息")
    @Size(max = 500, message = "竞争对手信息长度不能超过500个字符")
    private String competitionInfo;

    @Schema(description = "下一步行动计划")
    @Size(max = 500, message = "下一步行动计划长度不能超过500个字符")
    private String nextAction;

    @Schema(description = "下次跟进日期")
    private LocalDate nextFollowDate;

    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    @Schema(description = "所属部门ID")
    private Long deptId;

    @Schema(description = "标签(多个标签用逗号分隔)")
    @Size(max = 200, message = "标签长度不能超过200个字符")
    private String tags;

    @Schema(description = "备注")
    private String remark;
}