package com.hntsz.boot.modules.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.time.LocalDate;

/**
 * 合同伙伴关联表实体对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@TableName("contract_partner_relation")
public class ContractPartnerRelation extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 伙伴ID
     */
    private Long partnerId;

    /**
     * 伙伴角色(关联字典编码：partner_role)
     */
    private String partnerRole;

    /**
     * 角色描述(关联字典编码：partner_role_desc)
     */
    private String partnerRoleDesc;

    /**
     * 签署人
     */
    private String signingPerson;

    /**
     * 签署人职务
     */
    private String signingPersonTitle;

    /**
     * 签署日期
     */
    private LocalDate signingDate;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 更新人ID
     */
    private Long updateBy;
}
