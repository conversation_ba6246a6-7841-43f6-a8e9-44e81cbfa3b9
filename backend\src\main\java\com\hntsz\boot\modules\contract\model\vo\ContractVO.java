package com.hntsz.boot.modules.contract.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hntsz.boot.common.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 合同主表视图对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
public class ContractVO extends BaseVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同类型(关联字典编码：contract_type)
     */
    private String contractType;

    /**
     * 合同类型标签
     */
    private String contractTypeLabel;

    /**
     * 合同分类(关联字典编码：contract_category)
     */
    private String contractCategory;

    /**
     * 合同分类标签
     */
    private String contractCategoryLabel;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 关联商机ID(关联opportunity表)
     */
    private Long opportunityId;

    /**
     * 签署日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate signingDate;

    /**
     * 生效日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate effectiveDate;

    /**
     * 到期日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate expiryDate;

    /**
     * 合同状态(draft-草稿 pending-待签署 active-已生效 completed-已完成 terminated-已终止 cancelled-已作废)
     */
    private String contractStatus;

    /**
     * 付款方式(关联字典编码：payment_method)
     */
    private String paymentMethod;

    /**
     * 付款方式标签
     */
    private String paymentMethodLabel;

    /**
     * 签署地点
     */
    private String signingLocation;

    /**
     * 负责人ID(关联sys_user表)
     */
    private Long responsibleUserId;

    /**
     * 负责人姓名
     */
    private String responsibleUserName;

    /**
     * 所属部门ID(关联sys_dept表)
     */
    private Long deptId;

    /**
     * 所属部门名称
     */
    private String deptName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合同伙伴列表
     */
    private List<ContractPartnerRelationVO> parties;

    /**
     * 合同文件列表
     */
    private List<ContractAttachmentVO> attachments;

    /**
     * 收付款总金额
     */
    private BigDecimal totalPaymentAmount;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
