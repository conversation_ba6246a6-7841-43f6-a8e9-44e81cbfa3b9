<template>
  <div class="error-page">
    <div class="error-content">
      <van-image
        :src="'/404-illustration.svg'"
        alt="404"
        class="error-image"
      />
      <h1 class="error-title">页面不存在</h1>
      <p class="error-description">
        抱歉，您访问的页面不存在或已被删除
      </p>
      <div class="error-actions">
        <van-button
          type="primary"
          round
          @click="goHome"
        >
          返回首页
        </van-button>
        <van-button
          type="default"
          round
          @click="goBack"
        >
          返回上页
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const goHome = () => {
  router.replace('/home')
}

const goBack = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.error-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 400px;
  
  .error-image {
    width: 200px;
    height: 150px;
    margin-bottom: 24px;
  }
  
  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: #323233;
    margin: 0 0 12px 0;
  }
  
  .error-description {
    font-size: 16px;
    color: #646566;
    line-height: 1.6;
    margin: 0 0 32px 0;
  }
  
  .error-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    
    .van-button {
      min-width: 100px;
    }
  }
}
</style>