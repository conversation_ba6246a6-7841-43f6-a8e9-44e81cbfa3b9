import request from '@/utils/request';
import { AxiosPromise } from 'axios';

/**
 * 商机线索查询参数
 */
export interface OpportunityPageQuery extends PageQuery {
  opportunityCode?: string;
  opportunityName?: string;
  opportunityType?: string;
  opportunitySource?: string;
  partnerId?: number;
  opportunityStage?: string;
  opportunityStatus?: string;
  priority?: string;
  responsibleUserId?: number;
  deptId?: number;
  minEstimatedAmount?: number;
  maxEstimatedAmount?: number;
  minWinProbability?: number;
  maxWinProbability?: number;
  estimatedCloseStartDate?: string;
  estimatedCloseEndDate?: string;
  actualCloseStartDate?: string;
  actualCloseEndDate?: string;
  createStartTime?: string;
  createEndTime?: string;
  lostReason?: string;
  tags?: string;
}

/**
 * 商机线索分页对象
 */
export interface OpportunityPageVO {
  id?: number;
  opportunityCode?: string;
  opportunityName?: string;
  opportunityType?: string;
  opportunityTypeLabel?: string;
  opportunitySource?: string;
  opportunitySourceLabel?: string;
  partnerId?: number;
  partnerName?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  opportunityStage?: string;
  opportunityStageLabel?: string;
  winProbability?: number;
  estimatedAmount?: number;
  estimatedCloseDate?: string;
  actualCloseDate?: string;
  opportunityStatus?: string;
  opportunityStatusLabel?: string;
  lostReason?: string;
  lostReasonLabel?: string;
  priority?: string;
  priorityLabel?: string;
  productInterest?: string;
  requirements?: string;
  competitionInfo?: string;
  nextAction?: string;
  nextFollowDate?: string;
  responsibleUserId?: number;
  responsibleUserName?: string;
  deptId?: number;
  deptName?: string;
  tags?: string;
  remark?: string;
  followCount?: number;
  lastFollowDate?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 商机线索表单类型
 */
export interface OpportunityForm {
  id?: number;
  opportunityCode?: string;
  opportunityName?: string;
  opportunityType?: string;
  opportunityTypeLabel?: string;
  opportunitySource?: string;
  opportunitySourceLabel?: string;
  partnerId?: number;
  partnerName?: string;
  contactPerson?: string;
  contactPhone?: string;
  contactEmail?: string;
  opportunityStage?: string;
  opportunityStageLabel?: string;
  winProbability?: number;
  estimatedAmount?: number;
  estimatedCloseDate?: string;
  actualCloseDate?: string;
  opportunityStatus?: string;
  opportunityStatusLabel?: string;
  lostReason?: string;
  lostReasonLabel?: string;
  priority?: string;
  priorityLabel?: string;
  productInterest?: string;
  requirements?: string;
  competitionInfo?: string;
  nextAction?: string;
  nextFollowDate?: string;
  responsibleUserId?: number;
  responsibleUserName?: string;
  deptId?: number;
  deptName?: string;
  tags?: string;
  remark?: string;
  followCount?: number;
  lastFollowDate?: string;
  createTime?: string;
  updateTime?: string;
}

/**
 * 商机线索API
 */
class OpportunityAPI {
  /**
   * 获取商机线索分页列表
   *
   * @param queryParams 查询参数
   */
  static getPage(queryParams: OpportunityPageQuery): AxiosPromise<PageResult<OpportunityPageVO[]>> {
    return request({
      url: '/api/v1/opportunities/page',
      method: 'get',
      params: queryParams,
    });
  }

  /**
   * 获取商机线索详情
   *
   * @param id 商机ID
   */
  static getDetail(id: number): AxiosPromise<OpportunityPageVO> {
    return request({
      url: `/api/v1/opportunities/${id}`,
      method: 'get',
    });
  }

  /**
   * 获取商机线索表单数据
   *
   * @param id 商机ID
   */
  static getFormData(id: number): AxiosPromise<OpportunityForm> {
    return request({
      url: `/api/v1/opportunities/${id}/form`,
      method: 'get',
    });
  }

  /**
   * 新增商机线索
   *
   * @param data 表单数据
   */
  static create(data: OpportunityForm): AxiosPromise<any> {
    return request({
      url: '/api/v1/opportunities',
      method: 'post',
      data: data,
    });
  }

  /**
   * 修改商机线索
   *
   * @param id 商机ID
   * @param data 表单数据
   */
  static update(id: number, data: OpportunityForm): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunities/${id}`,
      method: 'put',
      data: data,
    });
  }

  /**
   * 删除商机线索
   *
   * @param ids 商机ID，多个以英文逗号(,)分割
   */
  static delete(ids: string): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunities/${ids}`,
      method: 'delete',
    });
  }

  /**
   * 获取商机线索选项列表
   */
  static getOptions(): AxiosPromise<OptionType[]> {
    return request({
      url: '/api/v1/opportunities/options',
      method: 'get',
    });
  }

  /**
   * 生成商机编码
   */
  static generateCode(): AxiosPromise<string> {
    return request({
      url: '/api/v1/opportunities/generate-code',
      method: 'get',
    });
  }

  /**
   * 批量转移商机负责人
   *
   * @param opportunityIds 商机ID列表
   * @param responsibleUserId 新负责人ID
   */
  static transfer(opportunityIds: number[], responsibleUserId: number): AxiosPromise<any> {
    return request({
      url: '/api/v1/opportunities/transfer',
      method: 'put',
      params: {
        opportunityIds: opportunityIds,
        responsibleUserId: responsibleUserId,
      },
    });
  }

  /**
   * 更新商机阶段
   *
   * @param id 商机ID
   * @param opportunityStage 商机阶段
   * @param winProbability 成单概率
   */
  static updateStage(id: number, opportunityStage: string, winProbability?: number): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunities/${id}/stage`,
      method: 'put',
      params: {
        opportunityStage: opportunityStage,
        winProbability: winProbability,
      },
    });
  }

  /**
   * 成交商机
   *
   * @param id 商机ID
   */
  static win(id: number): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunities/${id}/win`,
      method: 'put',
    });
  }

  /**
   * 失败商机
   *
   * @param id 商机ID
   * @param lostReason 失败原因
   */
  static lose(id: number, lostReason: string): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunities/${id}/lose`,
      method: 'put',
      params: {
        lostReason: lostReason,
      },
    });
  }

  /**
   * 归档商机
   *
   * @param ids 商机ID，多个以英文逗号(,)分割
   * @param archiveReason 归档原因
   */
  static archive(ids: string, archiveReason: string): AxiosPromise<any> {
    return request({
      url: `/api/v1/opportunities/${ids}/archive`,
      method: 'put',
      params: {
        archiveReason: archiveReason,
      },
    });
  }
}

export default OpportunityAPI;