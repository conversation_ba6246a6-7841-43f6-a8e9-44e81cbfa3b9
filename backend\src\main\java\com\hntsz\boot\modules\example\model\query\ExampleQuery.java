package com.hntsz.boot.modules.example.model.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import com.hntsz.boot.common.base.BasePageQuery;
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "示例查询对象")
public class ExampleQuery extends BasePageQuery {
    
    @Schema(description = "名称")
    private String name;

    @Schema(description = "大于年龄")
    private Integer greaterThanAge;

    @Schema(description = "小于年龄")
    private Integer lessThanAge;

}
