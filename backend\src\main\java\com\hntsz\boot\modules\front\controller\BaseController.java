package com.hntsz.boot.modules.front.controller;

import org.apache.commons.lang3.StringUtils;

import com.hntsz.boot.common.enums.Locale;

public class BaseController {

    protected String getAcceptLanguageToLocale(String acceptLanguage) {
        if (StringUtils.isBlank(acceptLanguage)) {
            return Locale.ZH.getValue();
        } else if (acceptLanguage.toLowerCase().contains("zh-cn")) {
            return Locale.ZH.getValue();
        } else if (acceptLanguage.toLowerCase().contains("zh")) {
            return Locale.ZH.getValue();
        } else {
            return Locale.EN.getValue();
        }
    }

}
