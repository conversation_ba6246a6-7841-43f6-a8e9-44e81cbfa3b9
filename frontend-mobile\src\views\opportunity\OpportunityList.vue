<template>
  <div class="opportunity-list-page">
    <!-- 头部搜索 -->
    <div class="search-header">
      <van-search
        v-model="searchKeyword"
        placeholder="搜索商机名称、编号"
        @search="handleSearch"
        @clear="handleClear"
      />
      <van-icon name="filter-o" class="filter-icon" @click="showFilterPopup = true" />
    </div>

    <!-- 标签筛选 -->
    <div class="filter-tabs">
      <van-tabs v-model:active="activeStatus" @change="handleStatusChange">
        <van-tab title="全部" name="" />
        <van-tab title="进行中" name="active" />
        <van-tab title="已成交" name="won" />
        <van-tab title="已失败" name="lost" />
      </van-tabs>
    </div>

    <!-- 商机列表 -->
    <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <van-list
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
      >
        <div v-for="opportunity in opportunityList" :key="opportunity.id" class="opportunity-item">
          <div class="item-header" @click="goToDetail(opportunity.id!)">
            <div class="title-section">
              <h3 class="opportunity-title">{{ opportunity.opportunityName }}</h3>
              <span :class="['status-tag', `status-${opportunity.opportunityStatus}`]">
                {{ opportunity.opportunityStatusLabel }}
              </span>
            </div>
            <div class="code">{{ opportunity.opportunityCode }}</div>
          </div>

          <div class="item-content">
            <div class="info-row">
              <span class="label">客户：</span>
              <span class="value">{{ opportunity.partnerName || '未知' }}</span>
            </div>
            <div class="info-row">
              <span class="label">阶段：</span>
              <span class="value">{{ opportunity.opportunityStageLabel }}</span>
              <span class="probability">{{ opportunity.winProbability }}%</span>
            </div>
            <div class="info-row">
              <span class="label">预估金额：</span>
              <span class="amount">¥{{ formatMoney(opportunity.estimatedAmount || 0) }}</span>
            </div>
            <div class="info-row">
              <span class="label">预计成交：</span>
              <span class="value">{{ formatDate(opportunity.estimatedCloseDate) || '未设置' }}</span>
            </div>
          </div>

          <div class="item-footer">
            <div class="meta-info">
              <span class="responsible">{{ opportunity.responsibleUserName }}</span>
              <span class="time">{{ formatRelativeTime(opportunity.createTime!) }}</span>
            </div>
            <div class="actions">
              <van-button
                type="primary"
                size="mini"
                @click="goToFollow(opportunity.id!)"
              >
                跟进
              </van-button>
              <van-button
                type="default"
                size="mini"
                @click="editOpportunity(opportunity.id!)"
              >
                编辑
              </van-button>
            </div>
          </div>
        </div>
      </van-list>
    </van-pull-refresh>

    <!-- 筛选弹窗 -->
    <van-popup v-model:show="showFilterPopup" position="bottom" :style="{ height: '60%' }">
      <div class="filter-popup">
        <div class="popup-header">
          <van-button type="default" @click="resetFilter">重置</van-button>
          <h3>筛选</h3>
          <van-button type="primary" @click="applyFilter">确定</van-button>
        </div>
        
        <div class="filter-content">
          <div class="filter-section">
            <h4>商机类型</h4>
            <van-checkbox-group v-model="filterForm.opportunityTypes">
              <van-checkbox
                v-for="type in opportunityTypes"
                :key="type.value"
                :name="type.value"
              >
                {{ type.label }}
              </van-checkbox>
            </van-checkbox-group>
          </div>

          <div class="filter-section">
            <h4>商机阶段</h4>
            <van-checkbox-group v-model="filterForm.opportunityStages">
              <van-checkbox
                v-for="stage in opportunityStages"
                :key="stage.value"
                :name="stage.value"
              >
                {{ stage.label }}
              </van-checkbox>
            </van-checkbox-group>
          </div>

          <div class="filter-section">
            <h4>预估金额</h4>
            <van-field
              v-model="filterForm.minAmount"
              type="number"
              placeholder="最小金额"
              label="¥"
            />
            <van-field
              v-model="filterForm.maxAmount"
              type="number"
              placeholder="最大金额"
              label="¥"
            />
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 浮动按钮 -->
    <van-floating-bubble
      icon="plus"
      @click="createOpportunity"
    />

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="chart-trending-o" to="/opportunity">商机</van-tabbar-item>
      <van-tabbar-item icon="description" to="/contract">合同</van-tabbar-item>
      <van-tabbar-item icon="contact" to="/partner">伙伴</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showSuccessToast, showFailToast } from 'vant'
import { opportunityApi } from '@/api/opportunity'
import type { Opportunity, OpportunityQuery } from '@/types/opportunity'
import type { OptionType } from '@/types/global'
import { formatMoney, formatDate, formatRelativeTime } from '@/utils/format'

const router = useRouter()

// 状态
const loading = ref(false)
const finished = ref(false)
const refreshing = ref(false)
const showFilterPopup = ref(false)
const activeTab = ref(1)
const activeStatus = ref('')
const searchKeyword = ref('')

// 商机列表
const opportunityList = ref<Opportunity[]>([])

// 分页参数
const pageParams = reactive({
  pageNum: 1,
  pageSize: 10
})

// 筛选表单
const filterForm = reactive({
  opportunityTypes: [] as string[],
  opportunityStages: [] as string[],
  minAmount: '',
  maxAmount: ''
})

// 字典选项
const opportunityTypes = ref<OptionType[]>([])
const opportunityStages = ref<OptionType[]>([])

// 加载商机列表
const loadOpportunityList = async (reset = false) => {
  if (loading.value) return

  loading.value = true

  try {
    if (reset) {
      pageParams.pageNum = 1
      finished.value = false
    }

    const query: OpportunityQuery = {
      pageNum: pageParams.pageNum,
      pageSize: pageParams.pageSize,
      opportunityStatus: activeStatus.value,
      keywords: searchKeyword.value || undefined,
      opportunityType: filterForm.opportunityTypes.length > 0 ? filterForm.opportunityTypes[0] : undefined,
      opportunityStage: filterForm.opportunityStages.length > 0 ? filterForm.opportunityStages[0] : undefined,
      minEstimatedAmount: filterForm.minAmount ? Number(filterForm.minAmount) : undefined,
      maxEstimatedAmount: filterForm.maxAmount ? Number(filterForm.maxAmount) : undefined
    }

    const result = await opportunityApi.getPage(query)

    if (reset) {
      opportunityList.value = result.records
    } else {
      opportunityList.value.push(...result.records)
    }

    // 判断是否还有更多数据
    if (result.records.length < pageParams.pageSize) {
      finished.value = true
    } else {
      pageParams.pageNum++
    }
  } catch (error) {
    console.error('加载商机列表失败:', error)
    showFailToast('加载失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

// 加载字典选项
const loadOptions = async () => {
  try {
    const options = await opportunityApi.getOptions()
    // 根据实际API返回的数据结构调整
    opportunityTypes.value = [
      { label: '新客户开发', value: 'new_customer' },
      { label: '老客户复购', value: 'existing_customer' },
      { label: '向上销售', value: 'upsell' }
    ]
    opportunityStages.value = [
      { label: '初步接触', value: 'initial' },
      { label: '有意向', value: 'interested' },
      { label: '方案阶段', value: 'proposal' },
      { label: '谈判阶段', value: 'negotiation' }
    ]
  } catch (error) {
    console.error('加载选项失败:', error)
  }
}

// 列表加载更多
const onLoad = () => {
  loadOpportunityList()
}

// 下拉刷新
const onRefresh = () => {
  loadOpportunityList(true)
}

// 搜索
const handleSearch = () => {
  loadOpportunityList(true)
}

// 清空搜索
const handleClear = () => {
  searchKeyword.value = ''
  loadOpportunityList(true)
}

// 状态筛选
const handleStatusChange = () => {
  loadOpportunityList(true)
}

// 应用筛选
const applyFilter = () => {
  showFilterPopup.value = false
  loadOpportunityList(true)
}

// 重置筛选
const resetFilter = () => {
  filterForm.opportunityTypes = []
  filterForm.opportunityStages = []
  filterForm.minAmount = ''
  filterForm.maxAmount = ''
}

// 跳转到详情页
const goToDetail = (id: number) => {
  router.push(`/opportunity/${id}`)
}

// 跳转到跟进页
const goToFollow = (id: number) => {
  router.push(`/opportunity/${id}/follow`)
}

// 编辑商机
const editOpportunity = (id: number) => {
  router.push(`/opportunity/${id}/edit`)
}

// 创建商机
const createOpportunity = () => {
  router.push('/opportunity/create')
}

// 组件挂载
onMounted(() => {
  loadOptions()
  loadOpportunityList(true)
})
</script>

<style scoped lang="scss">
.opportunity-list-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 60px;
}

.search-header {
  background: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  
  :deep(.van-search) {
    flex: 1;
    padding: 0;
  }
  
  .filter-icon {
    margin-left: 12px;
    font-size: 18px;
    color: #646566;
  }
}

.filter-tabs {
  background: white;
  
  :deep(.van-tabs__nav) {
    padding: 0 16px;
  }
}

.opportunity-item {
  background: white;
  margin-bottom: 12px;
  padding: 16px;
  
  .item-header {
    margin-bottom: 12px;
    
    .title-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 4px;
      
      .opportunity-title {
        font-size: 16px;
        font-weight: 500;
        color: #323233;
        margin: 0;
        flex: 1;
        margin-right: 8px;
      }
      
      .status-tag {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        
        &.status-active {
          background: #e6f2ff;
          color: #1989fa;
        }
        
        &.status-won {
          background: #e1f5e1;
          color: #07c160;
        }
        
        &.status-lost {
          background: #ffeaea;
          color: #ee0a24;
        }
      }
    }
    
    .code {
      font-size: 12px;
      color: #969799;
    }
  }
  
  .item-content {
    margin-bottom: 12px;
    
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 4px;
      font-size: 14px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #646566;
        width: 80px;
        flex-shrink: 0;
      }
      
      .value {
        color: #323233;
        flex: 1;
      }
      
      .amount {
        color: #ff976a;
        font-weight: 500;
      }
      
      .probability {
        margin-left: 8px;
        color: #07c160;
        font-weight: 500;
      }
    }
  }
  
  .item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .meta-info {
      font-size: 12px;
      color: #969799;
      
      .responsible {
        margin-right: 12px;
      }
    }
    
    .actions {
      display: flex;
      gap: 8px;
    }
  }
}

.filter-popup {
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #ebedf0;
    
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
    }
  }
  
  .filter-content {
    padding: 16px;
    max-height: calc(60vh - 60px);
    overflow-y: auto;
    
    .filter-section {
      margin-bottom: 24px;
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
      }
      
      :deep(.van-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
      
      :deep(.van-checkbox) {
        margin-right: 0;
        margin-bottom: 8px;
      }
      
      :deep(.van-field) {
        margin-bottom: 8px;
      }
    }
  }
}
</style>