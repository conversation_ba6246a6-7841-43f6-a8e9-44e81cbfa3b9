import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { showFailToast } from 'vant'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页',
      requiresAuth: true,
      showTabbar: true
    }
  },
  {
    path: '/opportunity',
    name: 'OpportunityList',
    component: () => import('@/views/opportunity/OpportunityList.vue'),
    meta: {
      title: '商机列表',
      requiresAuth: true,
      showTabbar: true
    }
  },
  {
    path: '/opportunity/:id',
    name: 'OpportunityDetail',
    component: () => import('@/views/opportunity/OpportunityDetail.vue'),
    meta: {
      title: '商机详情',
      requiresAuth: true
    }
  },
  {
    path: '/opportunity/:id/edit',
    name: 'OpportunityEdit',
    component: () => import('@/views/opportunity/OpportunityEdit.vue'),
    meta: {
      title: '编辑商机',
      requiresAuth: true
    }
  },
  {
    path: '/opportunity/create',
    name: 'OpportunityCreate',
    component: () => import('@/views/opportunity/OpportunityEdit.vue'),
    meta: {
      title: '新增商机',
      requiresAuth: true
    }
  },
  {
    path: '/opportunity/:id/follow',
    name: 'FollowList',
    component: () => import('@/views/opportunity/FollowList.vue'),
    meta: {
      title: '跟进记录',
      requiresAuth: true
    }
  },
  {
    path: '/opportunity/:opportunityId/follow/create',
    name: 'FollowCreate',
    component: () => import('@/views/opportunity/FollowEdit.vue'),
    meta: {
      title: '新增跟进',
      requiresAuth: true
    }
  },
  {
    path: '/opportunity/:opportunityId/follow/:id/edit',
    name: 'FollowEdit',
    component: () => import('@/views/opportunity/FollowEdit.vue'),
    meta: {
      title: '编辑跟进',
      requiresAuth: true
    }
  },
  {
    path: '/contract',
    name: 'ContractList',
    component: () => import('@/views/contract/ContractList.vue'),
    meta: {
      title: '合同列表',
      requiresAuth: true,
      showTabbar: true
    }
  },
  {
    path: '/contract/:id',
    name: 'ContractDetail',
    component: () => import('@/views/contract/ContractDetail.vue'),
    meta: {
      title: '合同详情',
      requiresAuth: true
    }
  },
  {
    path: '/contract/:id/edit',
    name: 'ContractEdit',
    component: () => import('@/views/contract/ContractEdit.vue'),
    meta: {
      title: '编辑合同',
      requiresAuth: true
    }
  },
  {
    path: '/contract/create',
    name: 'ContractCreate',
    component: () => import('@/views/contract/ContractEdit.vue'),
    meta: {
      title: '新增合同',
      requiresAuth: true
    }
  },
  {
    path: '/contract/:id/payment',
    name: 'PaymentList',
    component: () => import('@/views/contract/PaymentList.vue'),
    meta: {
      title: '付款记录',
      requiresAuth: true
    }
  },
  {
    path: '/partner',
    name: 'PartnerList',
    component: () => import('@/views/partner/PartnerList.vue'),
    meta: {
      title: '业务伙伴',
      requiresAuth: true,
      showTabbar: true
    }
  },
  {
    path: '/partner/:id',
    name: 'PartnerDetail',
    component: () => import('@/views/partner/PartnerDetail.vue'),
    meta: {
      title: '伙伴详情',
      requiresAuth: true
    }
  },
  {
    path: '/partner/:id/edit',
    name: 'PartnerEdit',
    component: () => import('@/views/partner/PartnerEdit.vue'),
    meta: {
      title: '编辑伙伴',
      requiresAuth: true
    }
  },
  {
    path: '/partner/create',
    name: 'PartnerCreate',
    component: () => import('@/views/partner/PartnerEdit.vue'),
    meta: {
      title: '新增伙伴',
      requiresAuth: true
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/profile/Profile.vue'),
    meta: {
      title: '个人中心',
      requiresAuth: true,
      showTabbar: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()
  
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - TSZ移动端`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      showFailToast('请先登录')
      next('/login')
      return
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (!userStore.userInfo) {
      try {
        await userStore.getUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
        next('/login')
        return
      }
    }
  }

  // 如果已登录，不允许访问登录页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/home')
    return
  }

  next()
})

export default router