// 认证相关类型定义

// 登录表单
export interface LoginForm {
  username: string
  password: string
  captchaKey?: string
  captchaCode?: string
  rememberMe?: boolean
}

// 登录结果
export interface LoginResult {
  accessToken: string
  refreshToken: string
  tokenType: string
  expiresIn: number
}

// 验证码信息
export interface CaptchaInfo {
  captchaKey: string
  captchaBase64: string
}

// OAuth配置
export interface OAuthConfig {
  clientId: string
  authorizationUrl: string
  redirectUri: string
  scopes: string
  logoutUrl: string
  enabled: boolean
}

// OAuth登录参数
export interface OAuthLoginParams {
  provider: string
  code: string
}