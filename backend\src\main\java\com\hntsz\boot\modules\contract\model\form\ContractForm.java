package com.hntsz.boot.modules.contract.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 合同主表表单对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@Schema(description = "合同主表表单对象")
public class ContractForm {

    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Long id;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "合同编号不能为空")
    @Size(max = 100, message = "合同编号长度不能超过100个字符")
    private String contractNo;

    /**
     * 合同名称
     */
    @Schema(description = "合同名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "合同名称不能为空")
    @Size(max = 200, message = "合同名称长度不能超过200个字符")
    private String contractName;

    /**
     * 合同类型(关联字典编码：contract_type)
     */
    @Schema(description = "合同类型")
    private String contractType;

    /**
     * 合同分类(关联字典编码：contract_category)
     */
    @Schema(description = "合同分类")
    private String contractCategory;

    /**
     * 合同金额
     */
    @Schema(description = "合同金额")
    private BigDecimal contractAmount;

    /**
     * 关联商机ID(关联opportunity表)
     */
    @Schema(description = "关联商机ID")
    private Long opportunityId;

    /**
     * 签署日期
     */
    @Schema(description = "签署日期")
    private LocalDate signingDate;

    /**
     * 生效日期
     */
    @Schema(description = "生效日期")
    private LocalDate effectiveDate;

    /**
     * 到期日期
     */
    @Schema(description = "到期日期")
    private LocalDate expiryDate;

    /**
     * 合同状态(draft-草稿 pending-待签署 active-已生效 completed-已完成 terminated-已终止 cancelled-已作废)
     */
    @Schema(description = "合同状态")
    private String contractStatus;

    /**
     * 付款方式(关联字典编码：payment_method)
     */
    @Schema(description = "付款方式")
    private String paymentMethod;

    /**
     * 签署地点
     */
    @Schema(description = "签署地点")
    @Size(max = 200, message = "签署地点长度不能超过200个字符")
    private String signingLocation;

    /**
     * 负责人ID(关联sys_user表)
     */
    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    /**
     * 所属部门ID(关联sys_dept表)
     */
    @Schema(description = "所属部门ID")
    private Long deptId;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;

    /**
     * 合同伙伴关联信息
     */
    @Schema(description = "合同伙伴关联信息")
    private List<ContractPartnerRelationForm> parties;
}
