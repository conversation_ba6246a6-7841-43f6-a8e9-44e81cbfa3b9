import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from 'unplugin-vue-components/resolvers'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
      vue(),
      AutoImport({
        imports: ['vue', 'vue-router', 'pinia'],
        resolvers: [VantResolver()],
        dts: true,
        eslintrc: {
          enabled: true
        }
      }),
      Components({
        resolvers: [VantResolver()],
        dts: true
      })
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src')
      }
    },
    css: {
      postcss: {
        plugins: [
          require('postcss-px-to-viewport')({
            viewportWidth: 375, // 视窗的宽度，对应设计稿宽度
            viewportHeight: 667, // 视窗的高度，对应设计稿高度
            unitPrecision: 3, // 指定`px`转换为视窗单位值的小数位数
            viewportUnit: 'vw', // 指定需要转换成的视窗单位
            selectorBlackList: ['.ignore', '.hairlines'], // 指定不转换为视窗单位的类
            minPixelValue: 1, // 小于或等于`1px`不转换为视窗单位
            mediaQuery: false // 允许在媒体查询中转换`px`
          })
        ]
      }
    },
    server: {
      host: '0.0.0.0',
      port: 5173,
      proxy: {
        '/api': {
          target: env.VITE_API_URL || 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: false,
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]'
        }
      }
    }
  }
})