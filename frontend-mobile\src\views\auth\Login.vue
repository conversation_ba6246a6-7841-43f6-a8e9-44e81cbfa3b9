<template>
  <div class="login-page">
    <div class="login-header">
      <img src="@/assets/logo.png" alt="Logo" class="logo" />
      <h1 class="title">TSZ移动端</h1>
      <p class="subtitle">企业管理平台</p>
    </div>

    <div class="login-form">
      <van-form @submit="handleLogin">
        <van-cell-group>
          <van-field
            v-model="form.username"
            name="username"
            label="用户名"
            placeholder="请输入用户名"
            :rules="[{ required: true, message: '请输入用户名' }]"
            left-icon="user-o"
            clearable
          />
          
          <van-field
            v-model="form.password"
            type="password"
            name="password"
            label="密码"
            placeholder="请输入密码"
            :rules="[{ required: true, message: '请输入密码' }]"
            left-icon="lock"
            clearable
          />
          
          <van-field
            v-if="showCaptcha"
            v-model="form.captchaCode"
            name="captchaCode"
            label="验证码"
            placeholder="请输入验证码"
            :rules="[{ required: true, message: '请输入验证码' }]"
            left-icon="shield-o"
            clearable
          >
            <template #button>
              <img
                v-if="captcha.captchaBase64"
                :src="captcha.captchaBase64"
                alt="验证码"
                class="captcha-image"
                @click="refreshCaptcha"
              />
            </template>
          </van-field>
        </van-cell-group>

        <div class="login-options">
          <van-checkbox v-model="form.rememberMe">记住我</van-checkbox>
          <a href="#" class="forgot-password">忘记密码？</a>
        </div>

        <div class="login-button">
          <van-button
            type="primary"
            block
            round
            :loading="loading"
            native-type="submit"
          >
            登录
          </van-button>
        </div>
      </van-form>

      <!-- OAuth登录 -->
      <div v-if="oauthConfig?.enabled" class="oauth-login">
        <van-divider>或</van-divider>
        <van-button
          type="default"
          block
          round
          @click="handleOAuthLogin"
        >
          第三方登录
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showSuccessToast, showFailToast } from 'vant'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { LoginForm, CaptchaInfo, OAuthConfig } from '@/types/auth'

const router = useRouter()
const userStore = useUserStore()

// 表单数据
const form = ref<LoginForm>({
  username: '',
  password: '',
  captchaKey: '',
  captchaCode: '',
  rememberMe: false
})

// 验证码信息
const captcha = ref<CaptchaInfo>({
  captchaKey: '',
  captchaBase64: ''
})

// OAuth配置
const oauthConfig = ref<OAuthConfig | null>(null)

// 状态
const loading = ref(false)
const showCaptcha = ref(false)

// 获取验证码
const getCaptcha = async () => {
  try {
    const result = await authApi.getCaptcha()
    captcha.value = result
    form.value.captchaKey = result.captchaKey
    showCaptcha.value = true
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
}

// 获取OAuth配置
const getOAuthConfig = async () => {
  try {
    const result = await authApi.getOAuthConfig()
    oauthConfig.value = result
  } catch (error) {
    console.error('获取OAuth配置失败:', error)
  }
}

// 处理登录
const handleLogin = async () => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    await userStore.login(form.value)
    showSuccessToast('登录成功')
    
    // 跳转到首页
    router.replace('/home')
  } catch (error: any) {
    console.error('登录失败:', error)
    
    // 如果是验证码错误，刷新验证码
    if (error.message?.includes('验证码')) {
      refreshCaptcha()
    }
    
    showFailToast(error.message || '登录失败')
  } finally {
    loading.value = false
  }
}

// 处理OAuth登录
const handleOAuthLogin = () => {
  if (!oauthConfig.value) return
  
  // 构建OAuth授权URL
  const params = new URLSearchParams({
    client_id: oauthConfig.value.clientId,
    redirect_uri: oauthConfig.value.redirectUri,
    scope: oauthConfig.value.scopes,
    response_type: 'code'
  })
  
  const authUrl = `${oauthConfig.value.authorizationUrl}?${params.toString()}`
  
  // 跳转到OAuth授权页面
  window.location.href = authUrl
}

// 组件挂载时获取配置
onMounted(() => {
  getCaptcha()
  getOAuthConfig()
})
</script>

<style scoped lang="scss">
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 20px;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  
  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
  }
  
  .title {
    font-size: 28px;
    font-weight: 600;
    color: #fff;
    margin: 0 0 8px 0;
  }
  
  .subtitle {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
  }
}

.login-form {
  background: #fff;
  border-radius: 16px;
  padding: 30px 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  
  :deep(.van-cell-group) {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
  }
  
  :deep(.van-cell) {
    padding: 16px;
  }
  
  :deep(.van-field__label) {
    width: 80px;
  }
}

.captcha-image {
  width: 80px;
  height: 32px;
  border-radius: 4px;
  cursor: pointer;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  
  .forgot-password {
    color: #1989fa;
    text-decoration: none;
    font-size: 14px;
  }
}

.login-button {
  margin-bottom: 20px;
  
  :deep(.van-button) {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}

.oauth-login {
  :deep(.van-divider__content) {
    color: #969799;
    font-size: 14px;
  }
  
  :deep(.van-button--default) {
    height: 48px;
    color: #646566;
    border-color: #ebedf0;
  }
}
</style>