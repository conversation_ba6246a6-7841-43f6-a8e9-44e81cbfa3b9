// 商机相关API
import request from '@/utils/request'
import type { 
  OpportunityQuery, 
  Opportunity, 
  OpportunityForm,
  FollowQuery,
  Follow,
  FollowForm 
} from '@/types/opportunity'
import type { PageResult, OptionType } from '@/types/global'

const OPPORTUNITY_BASE_URL = '/api/v1/opportunities'
const FOLLOW_BASE_URL = '/api/v1/opportunity-follows'

export const opportunityApi = {
  /**
   * 获取商机分页列表
   */
  getPage(params: OpportunityQuery) {
    return request<PageResult<Opportunity[]>>({
      url: `${OPPORTUNITY_BASE_URL}/page`,
      method: 'get',
      params
    })
  },

  /**
   * 获取商机详情
   */
  getDetail(id: number) {
    return request<Opportunity>({
      url: `${OPPORTUNITY_BASE_URL}/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取商机表单数据
   */
  getFormData(id: number) {
    return request<OpportunityForm>({
      url: `${OPPORTUNITY_BASE_URL}/${id}/form`,
      method: 'get'
    })
  },

  /**
   * 新增商机
   */
  create(data: OpportunityForm) {
    return request({
      url: OPPORTUNITY_BASE_URL,
      method: 'post',
      data
    })
  },

  /**
   * 修改商机
   */
  update(id: number, data: OpportunityForm) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除商机
   */
  delete(ids: string) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/${ids}`,
      method: 'delete'
    })
  },

  /**
   * 获取商机选项列表
   */
  getOptions() {
    return request<OptionType[]>({
      url: `${OPPORTUNITY_BASE_URL}/options`,
      method: 'get'
    })
  },

  /**
   * 生成商机编码
   */
  generateCode() {
    return request<string>({
      url: `${OPPORTUNITY_BASE_URL}/generate-code`,
      method: 'get'
    })
  },

  /**
   * 批量转移商机负责人
   */
  transfer(opportunityIds: number[], responsibleUserId: number) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/transfer`,
      method: 'put',
      params: {
        opportunityIds,
        responsibleUserId
      }
    })
  },

  /**
   * 更新商机阶段
   */
  updateStage(id: number, opportunityStage: string, winProbability?: number) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/${id}/stage`,
      method: 'put',
      params: {
        opportunityStage,
        winProbability
      }
    })
  },

  /**
   * 成交商机
   */
  win(id: number) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/${id}/win`,
      method: 'put'
    })
  },

  /**
   * 失败商机
   */
  lose(id: number, lostReason: string) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/${id}/lose`,
      method: 'put',
      params: { lostReason }
    })
  },

  /**
   * 归档商机
   */
  archive(ids: string, archiveReason: string) {
    return request({
      url: `${OPPORTUNITY_BASE_URL}/${ids}/archive`,
      method: 'put',
      params: { archiveReason }
    })
  }
}

export const followApi = {
  /**
   * 获取跟进记录分页列表
   */
  getPage(params: FollowQuery) {
    return request<PageResult<Follow[]>>({
      url: `${FOLLOW_BASE_URL}/page`,
      method: 'get',
      params
    })
  },

  /**
   * 获取跟进记录详情
   */
  getDetail(id: number) {
    return request<Follow>({
      url: `${FOLLOW_BASE_URL}/${id}`,
      method: 'get'
    })
  },

  /**
   * 新增跟进记录
   */
  create(data: FollowForm) {
    return request({
      url: FOLLOW_BASE_URL,
      method: 'post',
      data
    })
  },

  /**
   * 修改跟进记录
   */
  update(id: number, data: FollowForm) {
    return request({
      url: `${FOLLOW_BASE_URL}/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除跟进记录
   */
  delete(ids: string) {
    return request({
      url: `${FOLLOW_BASE_URL}/${ids}`,
      method: 'delete'
    })
  }
}