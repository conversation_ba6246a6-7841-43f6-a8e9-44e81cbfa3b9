<template>
  <el-drawer
    v-model="visible"
    title="合同详情"
    direction="rtl"
    size="800px"
    @close="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 详情内容 -->
    <div v-else-if="contractDetail" class="contract-detail">
      <!-- 基本信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="合同编号">
            {{ contractDetail.contractNo }}
          </el-descriptions-item>
          <el-descriptions-item label="合同名称">
            {{ contractDetail.contractName }}
          </el-descriptions-item>
          <el-descriptions-item label="合同类型">
            {{ contractDetail.contractTypeLabel || contractDetail.contractType }}
          </el-descriptions-item>
          <el-descriptions-item label="合同分类">
            {{ contractDetail.contractCategoryLabel || contractDetail.contractCategory }}
          </el-descriptions-item>
          <el-descriptions-item label="合同金额">
            <span v-if="contractDetail.contractAmount" class="amount-text">
              ¥{{ contractDetail.contractAmount.toLocaleString() }}
            </span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="付款方式">
            {{ contractDetail.paymentMethodLabel || contractDetail.paymentMethod }}
          </el-descriptions-item>
          <el-descriptions-item label="签署日期">
            {{ contractDetail.signingDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="生效日期">
            {{ contractDetail.effectiveDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="到期日期">
            {{ contractDetail.expiryDate || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="合同状态">
            <el-tag :type="getStatusType(contractDetail.contractStatus)">
              {{ getStatusLabel(contractDetail.contractStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="签署地点">
            {{ contractDetail.signingLocation || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ contractDetail.responsibleUserName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属部门">
            {{ contractDetail.deptName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ contractDetail.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">
            {{ contractDetail.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 伙伴信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>伙伴信息</h3>
          </div>
        </template>
        <el-table :data="contractDetail.parties || []" border>
          <el-table-column label="伙伴名称" prop="partnerName" width="200" />
          <el-table-column label="伙伴编码" prop="partnerCode" width="150" />
          <el-table-column label="伙伴类型" width="120">
            <template #default="scope">
              {{ scope.row.partnerTypeLabel || scope.row.partnerType }}
            </template>
          </el-table-column>
          <el-table-column label="伙伴角色" width="120">
            <template #default="scope">
              {{ scope.row.partnerRoleLabel || scope.row.partnerRole }}
            </template>
          </el-table-column>
          <el-table-column label="角色描述" width="120">
            <template #default="scope">
              {{ scope.row.partnerRoleDescLabel || scope.row.partnerRoleDesc }}
            </template>
          </el-table-column>
          <el-table-column label="签署人" prop="signingPerson" width="100" />
          <el-table-column label="签署人职务" prop="signingPersonTitle" width="120" />
          <el-table-column label="签署日期" prop="signingDate" width="120" />
        </el-table>
        <div v-if="!contractDetail.parties || contractDetail.parties.length === 0" class="no-data">
          暂无伙伴信息
        </div>
      </el-card>

      <!-- 文件列表 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>文件列表</h3>
          </div>
        </template>
        <el-table :data="contractDetail.attachments || []" border>
          <el-table-column label="文件名" prop="fileName" min-width="200">
            <template #default="scope">
              <el-link :href="getFileUrl(scope.row.filePath)" target="_blank" type="primary">
                {{ scope.row.fileName }}
              </el-link>
            </template>
          </el-table-column>
          <el-table-column label="文件类型" prop="fileType" width="100">
            <template #default="scope">
              <el-tag :type="getFileTypeColor(scope.row.fileType)" size="small">
                {{ getFileTypeLabel(scope.row.fileType) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="文件大小" prop="fileSize" width="100">
            <template #default="scope">
              {{ formatFileSize(scope.row.fileSize) }}
            </template>
          </el-table-column>
          <el-table-column label="文件描述" prop="fileDescription" min-width="150" />
        </el-table>
        <div v-if="!contractDetail.attachments || contractDetail.attachments.length === 0" class="no-data">
          暂无文件
        </div>
      </el-card>

    </div>

    <!-- 数据为空 -->
    <div v-else class="no-data">
      <el-empty description="合同不存在或已被删除" />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import { useRouter } from "vue-router";
import { ContractVO } from "@/api/contract/contract.api";
import ContractAPI from "@/api/contract/contract.api";
import FileAPI from "@/api/file.api";

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  contractId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["update:modelValue", "edit", "delete", "payment"]);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const router = useRouter();

const loading = ref(false);
const contractDetail = ref<ContractVO | null>(null);

function getStatusType(status: string): "info" | "warning" | "success" | "danger" {
  const statusMap: Record<string, "info" | "warning" | "success" | "danger"> = {
    draft: "info",
    pending: "warning",
    active: "success",
    completed: "success",
    terminated: "danger",
    cancelled: "danger",
  };
  return statusMap[status] || "info";
}

function getStatusLabel(status: string) {
  const statusMap: Record<string, string> = {
    draft: "草稿",
    pending: "待签署",
    active: "已生效",
    completed: "已完成",
    terminated: "已终止",
    cancelled: "已作废",
  };
  return statusMap[status] || status;
}

function getFileUrl(filePath: string): string {
  return FileAPI.getFullUrl(filePath);
}

function getFileTypeColor(fileType: string): "success" | "info" | "warning" | "danger" {
  const typeMap: Record<string, "success" | "info" | "warning" | "danger"> = {
    IMAGE: "success",
    VIDEO: "warning",
    DOCUMENT: "info",
    AUDIO: "warning",
    OTHER: "danger",
  };
  return typeMap[fileType] || "info";
}

function getFileTypeLabel(fileType: string): string {
  const typeMap: Record<string, string> = {
    IMAGE: "图片",
    VIDEO: "视频",
    DOCUMENT: "文档",
    AUDIO: "音频",
    OTHER: "其他",
  };
  return typeMap[fileType] || fileType;
}

function formatFileSize(size: number): string {
  if (!size) return "0 B";

  const units = ["B", "KB", "MB", "GB"];
  let index = 0;
  let fileSize = size;

  while (fileSize >= 1024 && index < units.length - 1) {
    fileSize /= 1024;
    index++;
  }

  return `${fileSize.toFixed(1)} ${units[index]}`;
}

function handleEdit() {
  emit("edit", props.contractId);
}

function handlePayment() {
  emit("payment", props.contractId);
}

function handleDelete() {
  emit("delete", props.contractId);
}

function loadContractDetail() {
  if (!props.contractId) {
    ElMessage.error("合同ID不能为空");
    handleClose();
    return;
  }

  loading.value = true;
  ContractAPI.getDetail(props.contractId)
    .then((data) => {
      contractDetail.value = data;
    })
    .catch(() => {
      ElMessage.error("获取合同详情失败");
      handleClose();
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleClose() {
  visible.value = false;
  contractDetail.value = null;
}

watch(
  () => props.modelValue,
  (val) => {
    if (val && props.contractId) {
      loadContractDetail();
    }
  }
);
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.contract-detail {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
}

.section-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.amount-text {
  color: #409eff;
  font-weight: 500;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 40px 0;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
  margin-top: 20px;
}

/* 表格样式优化 */
:deep(.el-table) {
  margin-top: 16px;
}

:deep(.el-descriptions) {
  margin-top: 16px;
}

:deep(.el-descriptions-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions-item__content) {
  color: #303133;
}

/* 空状态样式 */
.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  color: #909399;
  font-size: 14px;
}
</style>
