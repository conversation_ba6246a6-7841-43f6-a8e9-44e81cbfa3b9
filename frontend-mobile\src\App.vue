<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 应用生命周期
onMounted(() => {
  // 应用初始化
  initApp()
})

onUnmounted(() => {
  // 清理工作
})

const initApp = async () => {
  try {
    // 如果有token，尝试获取用户信息
    if (userStore.token) {
      await userStore.getUserInfo()
    }
  } catch (error) {
    console.error('App initialization failed:', error)
  }
}
</script>

<style>
#app {
  width: 100%;
  height: 100%;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
}
</style>