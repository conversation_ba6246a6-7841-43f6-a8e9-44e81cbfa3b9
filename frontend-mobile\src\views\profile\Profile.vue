<template>
  <div class="profile-page">
    <!-- 头部用户信息 -->
    <div class="profile-header">
      <div class="user-info">
        <van-image
          :src="userStore.avatar || '/avatar-placeholder.png'"
          alt="头像"
          class="avatar"
          round
          @click="changeAvatar"
        />
        <div class="user-text">
          <div class="username">{{ userStore.nickname || userStore.username }}</div>
          <div class="user-desc">{{ userStore.userInfo?.deptName || '未设置部门' }}</div>
        </div>
        <van-icon name="arrow" class="arrow-icon" @click="goToProfileEdit" />
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stats-item">
          <div class="stats-number">{{ stats.myOpportunities }}</div>
          <div class="stats-label">我的商机</div>
        </div>
        <div class="stats-item">
          <div class="stats-number">{{ stats.myContracts }}</div>
          <div class="stats-label">我的合同</div>
        </div>
        <div class="stats-item">
          <div class="stats-number">{{ stats.pendingFollows }}</div>
          <div class="stats-label">待跟进</div>
        </div>
        <div class="stats-item">
          <div class="stats-number">{{ stats.monthTarget }}</div>
          <div class="stats-label">月度目标</div>
        </div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="个人信息"
          icon="user-o"
          is-link
          @click="goToProfileEdit"
        />
        <van-cell
          title="我的消息"
          icon="chat-o"
          is-link
          @click="goToMessages"
        >
          <template #extra>
            <van-badge v-if="unreadCount > 0" :content="unreadCount" />
          </template>
        </van-cell>
        <van-cell
          title="我的收藏"
          icon="star-o"
          is-link
          @click="goToFavorites"
        />
        <van-cell
          title="数据统计"
          icon="chart-trending-o"
          is-link
          @click="goToStatistics"
        />
      </van-cell-group>
    </div>

    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="主题设置"
          icon="setting-o"
          is-link
          @click="showThemeActionSheet = true"
        >
          <template #extra>
            <span class="theme-current">{{ currentTheme }}</span>
          </template>
        </van-cell>
        <van-cell
          title="语言设置"
          icon="flag-o"
          is-link
          @click="showLanguageActionSheet = true"
        >
          <template #extra>
            <span class="language-current">{{ currentLanguage }}</span>
          </template>
        </van-cell>
        <van-cell
          title="清除缓存"
          icon="delete-o"
          is-link
          @click="clearCache"
        >
          <template #extra>
            <span class="cache-size">{{ cacheSize }}</span>
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <div class="menu-section">
      <van-cell-group>
        <van-cell
          title="关于我们"
          icon="info-o"
          is-link
          @click="goToAbout"
        />
        <van-cell
          title="意见反馈"
          icon="comment-o"
          is-link
          @click="goToFeedback"
        />
        <van-cell
          title="检查更新"
          icon="upgrade"
          is-link
          @click="checkUpdate"
        />
      </van-cell-group>
    </div>

    <!-- 退出登录 -->
    <div class="logout-section">
      <van-button
        type="danger"
        block
        round
        @click="handleLogout"
      >
        退出登录
      </van-button>
    </div>

    <!-- 主题选择 -->
    <van-action-sheet
      v-model:show="showThemeActionSheet"
      :actions="themeActions"
      cancel-text="取消"
      @select="onThemeSelect"
    />

    <!-- 语言选择 -->
    <van-action-sheet
      v-model:show="showLanguageActionSheet"
      :actions="languageActions"
      cancel-text="取消"
      @select="onLanguageSelect"
    />

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="chart-trending-o" to="/opportunity">商机</van-tabbar-item>
      <van-tabbar-item icon="description" to="/contract">合同</van-tabbar-item>
      <van-tabbar-item icon="contact" to="/partner">伙伴</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showConfirmDialog, showSuccessToast, showFailToast, showNotify } from 'vant'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()

// 状态
const activeTab = ref(4)
const showThemeActionSheet = ref(false)
const showLanguageActionSheet = ref(false)
const unreadCount = ref(3)
const cacheSize = ref('2.3MB')
const currentTheme = ref('跟随系统')
const currentLanguage = ref('简体中文')

// 统计数据
const stats = reactive({
  myOpportunities: 15,
  myContracts: 8,
  pendingFollows: 3,
  monthTarget: 50
})

// 主题选项
const themeActions = [
  { name: '跟随系统', value: 'auto' },
  { name: '浅色主题', value: 'light' },
  { name: '深色主题', value: 'dark' }
]

// 语言选项
const languageActions = [
  { name: '简体中文', value: 'zh-CN' },
  { name: '繁体中文', value: 'zh-TW' },
  { name: 'English', value: 'en-US' }
]

// 修改头像
const changeAvatar = () => {
  showNotify({
    type: 'primary',
    message: '头像修改功能开发中...'
  })
}

// 跳转到个人信息编辑
const goToProfileEdit = () => {
  showNotify({
    type: 'primary',
    message: '个人信息编辑功能开发中...'
  })
}

// 跳转到消息页面
const goToMessages = () => {
  showNotify({
    type: 'primary',
    message: '消息功能开发中...'
  })
}

// 跳转到收藏页面
const goToFavorites = () => {
  showNotify({
    type: 'primary',
    message: '收藏功能开发中...'
  })
}

// 跳转到统计页面
const goToStatistics = () => {
  showNotify({
    type: 'primary',
    message: '统计功能开发中...'
  })
}

// 主题选择
const onThemeSelect = (action: any) => {
  currentTheme.value = action.name
  showSuccessToast(`已切换到${action.name}`)
  showThemeActionSheet.value = false
}

// 语言选择
const onLanguageSelect = (action: any) => {
  currentLanguage.value = action.name
  showSuccessToast(`已切换到${action.name}`)
  showLanguageActionSheet.value = false
}

// 清除缓存
const clearCache = async () => {
  try {
    await showConfirmDialog({
      title: '确认清除',
      message: '确定要清除所有缓存数据吗？',
      confirmButtonText: '清除',
      confirmButtonColor: '#ee0a24'
    })
    
    // 清除缓存逻辑
    cacheSize.value = '0MB'
    showSuccessToast('缓存清除成功')
  } catch {
    // 用户取消
  }
}

// 跳转到关于页面
const goToAbout = () => {
  showNotify({
    type: 'primary',
    message: '关于页面开发中...'
  })
}

// 跳转到反馈页面
const goToFeedback = () => {
  showNotify({
    type: 'primary',
    message: '反馈功能开发中...'
  })
}

// 检查更新
const checkUpdate = () => {
  showNotify({
    type: 'success',
    message: '当前已是最新版本'
  })
}

// 退出登录
const handleLogout = async () => {
  try {
    await showConfirmDialog({
      title: '确认退出',
      message: '确定要退出登录吗？',
      confirmButtonText: '退出',
      confirmButtonColor: '#ee0a24'
    })
    
    await userStore.logout()
    showSuccessToast('退出成功')
    router.replace('/login')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('退出登录失败:', error)
      showFailToast('退出失败')
    }
  }
}

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    // 这里应该调用API获取用户统计数据
    // stats 已经在 reactive 中初始化了模拟数据
  } catch (error) {
    console.error('加载用户统计失败:', error)
  }
}

// 组件挂载
onMounted(() => {
  loadUserStats()
})
</script>

<style scoped lang="scss">
.profile-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 60px;
}

.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px 30px;
  color: white;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 70px;
      height: 70px;
      margin-right: 16px;
      cursor: pointer;
    }
    
    .user-text {
      flex: 1;
      
      .username {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .user-desc {
        font-size: 14px;
        opacity: 0.8;
      }
    }
    
    .arrow-icon {
      color: white;
      opacity: 0.6;
    }
  }
}

.stats-section {
  padding: 0 16px;
  margin-top: -20px;
  margin-bottom: 20px;
  
  .stats-grid {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .stats-item {
      text-align: center;
      
      .stats-number {
        font-size: 20px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 8px;
      }
      
      .stats-label {
        font-size: 12px;
        color: #969799;
      }
    }
  }
}

.menu-section {
  margin-bottom: 12px;
  
  :deep(.van-cell-group) {
    margin: 0 16px;
    border-radius: 12px;
    overflow: hidden;
  }
  
  :deep(.van-cell) {
    padding: 16px;
    
    .van-cell__title {
      font-size: 15px;
    }
    
    .van-cell__left-icon {
      margin-right: 12px;
      font-size: 18px;
    }
  }
  
  .theme-current,
  .language-current,
  .cache-size {
    font-size: 14px;
    color: #969799;
  }
}

.logout-section {
  padding: 20px 16px;
  
  :deep(.van-button) {
    height: 48px;
    font-size: 16px;
    font-weight: 500;
  }
}
</style>