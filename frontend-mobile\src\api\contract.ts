// 合同相关API
import request from '@/utils/request'
import type {
  ContractQuery,
  Contract,
  ContractForm,
  PartnerQuery,
  Partner,
  PartnerForm,
  PaymentQuery,
  Payment,
  PaymentForm
} from '@/types/contract'
import type { PageResult, OptionType } from '@/types/global'

const CONTRACT_BASE_URL = '/api/v1/contracts'
const PARTNER_BASE_URL = '/api/v1/partners'
const PAYMENT_BASE_URL = '/api/v1/contract-payments'

export const contractApi = {
  /**
   * 获取合同分页列表
   */
  getPage(params: ContractQuery) {
    return request<PageResult<Contract[]>>({
      url: `${CONTRACT_BASE_URL}/page`,
      method: 'get',
      params
    })
  },

  /**
   * 获取合同详情
   */
  getDetail(id: string) {
    return request<Contract>({
      url: `${CONTRACT_BASE_URL}/${id}`,
      method: 'get'
    })
  },

  /**
   * 获取合同表单数据
   */
  getFormData(id: string) {
    return request<ContractForm>({
      url: `${CONTRACT_BASE_URL}/${id}/form`,
      method: 'get'
    })
  },

  /**
   * 新增合同
   */
  create(data: ContractForm) {
    return request<string>({
      url: CONTRACT_BASE_URL,
      method: 'post',
      data
    })
  },

  /**
   * 修改合同
   */
  update(id: string, data: ContractForm) {
    return request({
      url: `${CONTRACT_BASE_URL}/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除合同
   */
  delete(ids: string) {
    return request({
      url: `${CONTRACT_BASE_URL}/${ids}`,
      method: 'delete'
    })
  },

  /**
   * 获取合同选项列表
   */
  getOptions() {
    return request<OptionType[]>({
      url: `${CONTRACT_BASE_URL}/options`,
      method: 'get'
    })
  },

  /**
   * 更新合同状态
   */
  updateStatus(id: string, status: string) {
    return request({
      url: `${CONTRACT_BASE_URL}/${id}/status`,
      method: 'put',
      params: { status }
    })
  },

  /**
   * 根据伙伴ID获取相关合同列表
   */
  getByPartnerId(partnerId: string) {
    return request<Contract[]>({
      url: `${CONTRACT_BASE_URL}/by-partner/${partnerId}`,
      method: 'get'
    })
  }
}

export const partnerApi = {
  /**
   * 获取业务伙伴分页列表
   */
  getPage(params: PartnerQuery) {
    return request<PageResult<Partner[]>>({
      url: `${PARTNER_BASE_URL}/page`,
      method: 'get',
      params
    })
  },

  /**
   * 获取业务伙伴详情
   */
  getDetail(id: string) {
    return request<Partner>({
      url: `${PARTNER_BASE_URL}/${id}`,
      method: 'get'
    })
  },

  /**
   * 新增业务伙伴
   */
  create(data: PartnerForm) {
    return request<string>({
      url: PARTNER_BASE_URL,
      method: 'post',
      data
    })
  },

  /**
   * 修改业务伙伴
   */
  update(id: string, data: PartnerForm) {
    return request({
      url: `${PARTNER_BASE_URL}/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除业务伙伴
   */
  delete(ids: string) {
    return request({
      url: `${PARTNER_BASE_URL}/${ids}`,
      method: 'delete'
    })
  },

  /**
   * 获取业务伙伴选项列表
   */
  getOptions() {
    return request<OptionType[]>({
      url: `${PARTNER_BASE_URL}/options`,
      method: 'get'
    })
  }
}

export const paymentApi = {
  /**
   * 获取付款记录分页列表
   */
  getPage(params: PaymentQuery) {
    return request<PageResult<Payment[]>>({
      url: `${PAYMENT_BASE_URL}/page`,
      method: 'get',
      params
    })
  },

  /**
   * 获取付款记录详情
   */
  getDetail(id: string) {
    return request<Payment>({
      url: `${PAYMENT_BASE_URL}/${id}`,
      method: 'get'
    })
  },

  /**
   * 新增付款记录
   */
  create(data: PaymentForm) {
    return request<string>({
      url: PAYMENT_BASE_URL,
      method: 'post',
      data
    })
  },

  /**
   * 修改付款记录
   */
  update(id: string, data: PaymentForm) {
    return request({
      url: `${PAYMENT_BASE_URL}/${id}`,
      method: 'put',
      data
    })
  },

  /**
   * 删除付款记录
   */
  delete(ids: string) {
    return request({
      url: `${PAYMENT_BASE_URL}/${ids}`,
      method: 'delete'
    })
  }
}