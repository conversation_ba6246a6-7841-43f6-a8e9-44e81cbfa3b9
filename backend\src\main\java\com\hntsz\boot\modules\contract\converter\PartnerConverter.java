package com.hntsz.boot.modules.contract.converter;

import com.hntsz.boot.modules.contract.model.entity.Partner;
import com.hntsz.boot.modules.contract.model.form.PartnerForm;
import com.hntsz.boot.modules.contract.model.vo.PartnerVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 业务伙伴对象转换器
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Mapper
public interface PartnerConverter {

    PartnerConverter INSTANCE = Mappers.getMapper(PartnerConverter.class);

    /**
     * 实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    PartnerVO entityToVO(Partner entity);

    /**
     * VO转换为实体
     *
     * @param vo VO对象
     * @return 实体对象
     */
    Partner voToEntity(PartnerVO vo);

    /**
     * 表单转换为实体
     *
     * @param form 表单对象
     * @return 实体对象
     */
    Partner formToEntity(PartnerForm form);

    /**
     * 实体转换为表单
     *
     * @param entity 实体对象
     * @return 表单对象
     */
    PartnerForm entityToForm(Partner entity);
}
