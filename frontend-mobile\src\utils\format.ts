// 格式化工具类
import dayjs from 'dayjs'

/**
 * 格式化金额
 * @param amount 金额
 * @param precision 精度
 */
export function formatMoney(amount: number | string, precision = 2): string {
  const num = Number(amount)
  if (isNaN(num)) return '0.00'
  
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: precision,
    maximumFractionDigits: precision
  })
}

/**
 * 格式化日期
 * @param date 日期
 * @param format 格式
 */
export function formatDate(date: string | Date, format = 'YYYY-MM-DD'): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化日期时间
 * @param date 日期时间
 * @param format 格式
 */
export function formatDateTime(date: string | Date, format = 'YYYY-MM-DD HH:mm:ss'): string {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 格式化相对时间
 * @param date 日期
 */
export function formatRelativeTime(date: string | Date): string {
  if (!date) return ''
  
  const now = dayjs()
  const target = dayjs(date)
  const diff = now.diff(target, 'minute')
  
  if (diff < 1) return '刚刚'
  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  if (diff < 43200) return `${Math.floor(diff / 1440)}天前`
  
  return target.format('YYYY-MM-DD')
}

/**
 * 格式化文件大小
 * @param size 文件大小（字节）
 */
export function formatFileSize(size: number): string {
  if (size === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  
  return parseFloat((size / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 格式化手机号
 * @param phone 手机号
 */
export function formatPhone(phone: string): string {
  if (!phone) return ''
  
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
  }
  
  return phone
}

/**
 * 格式化身份证号
 * @param idCard 身份证号
 */
export function formatIdCard(idCard: string): string {
  if (!idCard) return ''
  
  if (idCard.length === 18) {
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
  }
  
  return idCard
}

/**
 * 格式化银行卡号
 * @param bankCard 银行卡号
 */
export function formatBankCard(bankCard: string): string {
  if (!bankCard) return ''
  
  const cleaned = bankCard.replace(/\D/g, '')
  if (cleaned.length >= 16) {
    return cleaned.replace(/(\d{4})\d*(\d{4})/, '$1****$2')
  }
  
  return bankCard
}

/**
 * 截取字符串
 * @param str 字符串
 * @param length 长度
 */
export function truncate(str: string, length: number): string {
  if (!str) return ''
  
  if (str.length <= length) return str
  
  return str.substring(0, length) + '...'
}

/**
 * 高亮搜索关键词
 * @param text 文本
 * @param keyword 关键词
 */
export function highlightKeyword(text: string, keyword: string): string {
  if (!text || !keyword) return text
  
  const regex = new RegExp(keyword, 'gi')
  return text.replace(regex, `<mark>$&</mark>`)
}