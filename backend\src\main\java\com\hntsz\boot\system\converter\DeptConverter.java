package com.hntsz.boot.system.converter;

import org.mapstruct.Mapper;

import com.hntsz.boot.system.model.entity.Dept;
import com.hntsz.boot.system.model.form.DeptForm;
import com.hntsz.boot.system.model.vo.DeptVO;

/**
 * 部门对象转换器
 *
 * <AUTHOR>
 * @since 2022/7/29
 */
@Mapper(componentModel = "spring")
public interface DeptConverter {

    DeptForm toForm(Dept entity);
    
    DeptVO toVo(Dept entity);

    Dept toEntity(DeptForm deptForm);

}