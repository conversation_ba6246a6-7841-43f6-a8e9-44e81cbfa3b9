package com.hntsz.boot.modules.opportunity.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 商机跟进记录查询参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机跟进记录查询参数")
public class OpportunityFollowQuery extends BasePageQuery {

    @Schema(description = "商机ID")
    private Long opportunityId;

    @Schema(description = "跟进方式")
    private String followType;

    @Schema(description = "跟进结果")
    private String followResult;

    @Schema(description = "跟进人ID")
    private Long followUserId;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "跟进开始日期")
    private LocalDate followStartDate;

    @Schema(description = "跟进结束日期")
    private LocalDate followEndDate;

    @Schema(description = "创建开始时间")
    private LocalDate createStartTime;

    @Schema(description = "创建结束时间")
    private LocalDate createEndTime;

    @Schema(description = "关键字(跟进内容、联系人、商机名称等)")
    private String keywords;
}