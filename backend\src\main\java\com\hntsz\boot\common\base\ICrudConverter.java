package com.hntsz.boot.common.base;

/**
 * 通用 CRUD 转换器接口
 *
 * @param <T> 实体类型
 * @param <V> VO类型
 * @param <D> Detail类型
 * @param <CF> Create Form类型
 * @param <UF> Update Form类型
 */
public interface ICrudConverter<T, V, D, CF, UF> {

    /**
     * 将实体转换为VO
     *
     * @param entity 实体对象
     * @return VO对象
     */
    V entityToVo(T entity);

    /**
     * 将实体转换为详情
     *
     * @param entity 实体对象
     * @return 详情对象
     */
    D entityToDetail(T entity);

    /**
     * 将VO转换为实体
     *
     * @param vo VO对象
     * @return 实体对象
     */
    T voToEntity(V vo);

    /**
     * 将Form转换为实体
     *
     * @param form Form对象
     * @return 实体对象
     */
    T createFormToEntity(CF form);

    /**
     * 将Update Form转换为实体
     *
     * @param form Update Form对象
     * @return 实体对象
     */
    T updateFormToEntity(UF form);
} 