package com.hntsz.boot.modules.contract.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hntsz.boot.modules.contract.model.entity.ContractAttachment;
import com.hntsz.boot.modules.contract.model.vo.ContractAttachmentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同文件关联Mapper接口
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Mapper
public interface ContractAttachmentMapper extends BaseMapper<ContractAttachment> {

    /**
     * 根据合同ID获取文件列表
     *
     * @param contractId 合同ID
     * @return 文件列表
     */
    List<ContractAttachmentVO> getByContractId(@Param("contractId") Long contractId);

    /**
     * 根据合同ID删除文件关联
     *
     * @param contractId 合同ID
     * @return 删除数量
     */
    int deleteByContractId(@Param("contractId") Long contractId);

    /**
     * 根据附件ID删除文件关联
     *
     * @param attachmentId 附件ID
     * @return 删除数量
     */
    int deleteByAttachmentId(@Param("attachmentId") Long attachmentId);
}
