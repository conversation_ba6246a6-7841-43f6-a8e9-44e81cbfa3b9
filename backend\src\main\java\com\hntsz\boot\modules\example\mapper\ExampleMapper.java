package com.hntsz.boot.modules.example.mapper;



import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.hntsz.boot.common.annotation.DataPermission;
import com.hntsz.boot.modules.example.model.entity.Example;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExampleMapper extends BaseMapper<Example> {

    @Override
    List<Example> selectList(@Param(Constants.WRAPPER) Wrapper<Example> queryWrapper);
} 