# TSZ Mobile 移动端

基于 Vue 3 + TypeScript + Vant 的企业管理平台移动端应用。

## 项目简介

TSZ Mobile 是一个完整的企业管理平台移动端解决方案，主要功能包括：

- 🏠 **首页** - 数据概览、快捷操作、最近动态
- 💼 **商机管理** - 商机列表、详情查看、跟进记录、阶段管理
- 📄 **合同管理** - 合同列表、详情查看、付款记录管理
- 🤝 **业务伙伴** - 伙伴列表、详情管理、合同关联
- 👤 **个人中心** - 用户信息、设置、统计数据

## 技术栈

- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite 3
- **UI 组件库**: Vant 4
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP 客户端**: Axios
- **工具库**: VueUse、Day.js
- **移动端适配**: amfe-flexible + postcss-px-to-viewport

## 项目结构

```
frontend-mobile/
├── public/                 # 静态资源
├── src/
│   ├── api/               # API 接口
│   │   ├── auth.ts        # 认证相关
│   │   ├── opportunity.ts # 商机相关
│   │   └── contract.ts    # 合同相关
│   ├── assets/            # 资源文件
│   ├── components/        # 公共组件
│   ├── composables/       # 组合式函数
│   ├── router/            # 路由配置
│   ├── stores/            # 状态管理
│   │   └── user.ts        # 用户状态
│   ├── styles/            # 样式文件
│   │   └── index.scss     # 全局样式
│   ├── types/             # 类型定义
│   │   ├── global.d.ts    # 全局类型
│   │   ├── auth.ts        # 认证类型
│   │   ├── opportunity.ts # 商机类型
│   │   └── contract.ts    # 合同类型
│   ├── utils/             # 工具函数
│   │   ├── request.ts     # HTTP 请求
│   │   ├── auth.ts        # 认证工具
│   │   ├── storage.ts     # 存储工具
│   │   └── format.ts      # 格式化工具
│   ├── views/             # 页面组件
│   │   ├── auth/          # 认证页面
│   │   ├── opportunity/   # 商机页面
│   │   ├── contract/      # 合同页面
│   │   ├── partner/       # 伙伴页面
│   │   ├── profile/       # 个人中心
│   │   └── error/         # 错误页面
│   ├── App.vue
│   └── main.ts
├── .env.development       # 开发环境配置
├── .env.production        # 生产环境配置
├── index.html
├── package.json
├── tsconfig.json
└── vite.config.ts
```

## 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
cd frontend-mobile
pnpm install
```

### 开发运行

```bash
pnpm run dev
```

### 构建部署

```bash
# 生产环境构建
pnpm run build

# 预览构建结果
pnpm run preview
```

### 代码规范

```bash
# 代码检查
pnpm run lint

# 类型检查
pnpm run type-check
```

## 功能特性

### 移动端适配
- 响应式设计，支持多种屏幕尺寸
- 触摸手势支持
- 原生体验的交互动画

### 性能优化
- 路由懒加载
- 图片懒加载
- 虚拟列表（长列表优化）
- 请求防抖和节流

### 用户体验
- 下拉刷新、上拉加载
- 骨架屏加载状态
- 错误边界处理
- 离线缓存支持

### 安全特性
- JWT Token 认证
- 请求拦截和响应处理
- 敏感信息加密存储
- HTTPS 通信

## API 接口

移动端使用与 PC 端相同的后端 API，主要接口包括：

- **认证接口**: `/api/v1/auth/*`
- **商机接口**: `/api/v1/opportunities/*`
- **合同接口**: `/api/v1/contracts/*`
- **业务伙伴**: `/api/v1/partners/*`

## 部署说明

### 环境变量配置

根据部署环境修改对应的 `.env` 文件：

```bash
# API 基础地址
VITE_API_URL=https://your-api-server.com

# 应用标题
VITE_APP_TITLE=TSZ移动端

# 其他配置...
```

### 构建部署

```bash
# 构建
pnpm run build

# 部署到 Web 服务器
# 将 dist/ 目录下的文件上传到服务器
```

## 浏览器支持

- iOS Safari >= 10
- Android Chrome >= 60
- 现代移动浏览器

## 开发规范

### 代码风格
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 Vue 3 Composition API 规范
- TypeScript 严格模式

### 组件开发
- 使用 `<script setup>` 语法
- 组件文件使用 PascalCase 命名
- 样式使用 scoped 模式

### Git 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具相关
```

## 常见问题

### 1. 开发环境跨域问题
在 `vite.config.ts` 中已配置代理，确保 `VITE_API_URL` 环境变量正确。

### 2. 移动端调试
推荐使用 Chrome DevTools 的移动设备模拟器，或使用 vconsole 进行真机调试。

### 3. 构建优化
项目已配置代码分割和资源优化，如需进一步优化可调整 `vite.config.ts` 配置。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基础功能模块
- 完成移动端适配

## 许可证

MIT License