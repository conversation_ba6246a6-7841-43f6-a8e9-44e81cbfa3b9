// 认证相关API
import request from '@/utils/request'
import type { LoginForm, LoginResult, CaptchaInfo, OAuthConfig, OAuthLoginParams } from '@/types/auth'
import type { UserInfo } from '@/types/global'

const AUTH_BASE_URL = '/api/v1/auth'

export const authApi = {
  /**
   * 用户登录
   */
  login(data: LoginForm) {
    const formData = new FormData()
    formData.append('username', data.username)
    formData.append('password', data.password)
    if (data.captchaKey) formData.append('captchaKey', data.captchaKey)
    if (data.captchaCode) formData.append('captchaCode', data.captchaCode)

    return request<LoginResult>({
      url: `${AUTH_BASE_URL}/login`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 刷新token
   */
  refreshToken(refreshToken: string) {
    return request<LoginResult>({
      url: `${AUTH_BASE_URL}/refresh-token`,
      method: 'post',
      params: { refreshToken },
      headers: {
        Authorization: 'no-auth'
      }
    })
  },

  /**
   * 退出登录
   */
  logout() {
    return request({
      url: `${AUTH_BASE_URL}/logout`,
      method: 'delete'
    })
  },

  /**
   * 获取验证码
   */
  getCaptcha() {
    return request<CaptchaInfo>({
      url: `${AUTH_BASE_URL}/captcha`,
      method: 'get',
      headers: {
        Authorization: 'no-auth'
      }
    })
  },

  /**
   * OAuth登录
   */
  oauthLogin(params: OAuthLoginParams) {
    const formData = new FormData()
    formData.append('provider', params.provider)
    formData.append('code', params.code)

    return request<LoginResult>({
      url: `${AUTH_BASE_URL}/oauth/login`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取OAuth配置
   */
  getOAuthConfig() {
    return request<OAuthConfig>({
      url: `${AUTH_BASE_URL}/oauth/config`,
      method: 'get',
      headers: {
        Authorization: 'no-auth'
      }
    })
  },

  /**
   * 获取当前用户信息
   */
  getUserInfo() {
    return request<UserInfo>({
      url: '/api/v1/users/me',
      method: 'get'
    })
  }
}