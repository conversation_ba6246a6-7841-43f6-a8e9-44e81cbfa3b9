package com.hntsz.boot.modules.contract.model.form;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 合同文件关联表单对象
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Schema(description = "合同文件关联表单对象")
public class ContractAttachmentForm {

    /**
     * 合同ID(关联contract表)
     */
    @Schema(description = "合同ID")
    @NotNull(message = "合同ID不能为空")
    private Long contractId;

    /**
     * 附件ID(关联tsz_attachment表)
     */
    @Schema(description = "附件ID")
    @NotNull(message = "附件ID不能为空")
    private Long attachmentId;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sort;
}
