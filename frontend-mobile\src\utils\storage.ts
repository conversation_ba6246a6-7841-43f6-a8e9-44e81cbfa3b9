// 本地存储工具类

interface StorageOptions {
  expire?: number // 过期时间（毫秒）
}

interface StorageData {
  value: any
  expire?: number
}

class Storage {
  private storage: globalThis.Storage

  constructor(storage: globalThis.Storage) {
    this.storage = storage
  }

  /**
   * 设置存储项
   * @param key 键
   * @param value 值
   * @param options 选项
   */
  set(key: string, value: any, options?: StorageOptions): void {
    const data: StorageData = { value }
    
    if (options?.expire) {
      data.expire = Date.now() + options.expire
    }

    this.storage.setItem(key, JSON.stringify(data))
  }

  /**
   * 获取存储项
   * @param key 键
   * @param defaultValue 默认值
   * @returns 
   */
  get<T = any>(key: string, defaultValue?: T): T | null {
    const item = this.storage.getItem(key)
    
    if (!item) {
      return defaultValue ?? null
    }

    try {
      const data: StorageData = JSON.parse(item)
      
      // 检查是否过期
      if (data.expire && Date.now() > data.expire) {
        this.remove(key)
        return defaultValue ?? null
      }

      return data.value
    } catch {
      // 兼容旧版本数据
      return item as unknown as T
    }
  }

  /**
   * 移除存储项
   * @param key 键
   */
  remove(key: string): void {
    this.storage.removeItem(key)
  }

  /**
   * 清空存储
   */
  clear(): void {
    this.storage.clear()
  }

  /**
   * 获取所有键
   */
  keys(): string[] {
    return Object.keys(this.storage)
  }
}

// 创建实例
export const localStorage = new Storage(window.localStorage)
export const sessionStorage = new Storage(window.sessionStorage)

// 常用存储键
export const STORAGE_KEYS = {
  TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  THEME: 'theme',
  LANGUAGE: 'language'
} as const