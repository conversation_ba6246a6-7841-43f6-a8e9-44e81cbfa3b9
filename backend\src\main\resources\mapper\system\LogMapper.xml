<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hntsz.boot.system.mapper.LogMapper">

    <!-- 日志分页列表 -->
    <select id="getLogPage" resultType="com.hntsz.boot.system.model.vo.LogPageVO">
        SELECT
            t1.id,
            t1.module,
            t1.content,
            t1.request_uri,
            t1.ip,
            CONCAT(t1.province," ", t1.city) AS region,
            t1.execution_time,
            CONCAT(t1.browser," ", t1.browser_version) AS browser,
            t1.os,
            t1.create_time,
            t2.nickname AS operator
        FROM
            sys_log t1
            LEFT JOIN sys_user t2 ON t1.create_by = t2.id
        <where>
            t1.is_deleted = 0
            <if test="queryParams.keywords != null and queryParams.keywords != ''">
                AND (
                    t1.content LIKE concat('%',#{queryParams.keywords},'%')
                    OR
                    t1.ip LIKE concat('%',#{queryParams.keywords},'%')
                    OR
                    t2.nickname LIKE concat('%',#{queryParams.keywords},'%')
                )
            </if>
            <if test="queryParams.createTime != null and queryParams.createTime.size > 0">
                <if test="queryParams.createTime[0] != null and queryParams.createTime[0] != ''">
                    <bind name="startDate" value="queryParams.createTime[0].length() == 10 ? queryParams.createTime[0] + ' 00:00:00' : queryParams.createTime[0]"/>
                    AND t1.create_time &gt;= #{startDate}
                </if>
                <if test="queryParams.createTime[1] != null and queryParams.createTime[1] != ''">
                    <bind name="endDate" value="queryParams.createTime[1].length() == 10 ? queryParams.createTime[1] + ' 23:59:59' : queryParams.createTime[1]"/>
                    AND t1.create_time &lt;= #{endDate}
                </if>
            </if>
        </where>
        ORDER BY
            t1.create_time DESC
    </select>

    <!-- 获取访问量日统计列表  -->
    <select id="getPvCounts" resultType="com.hntsz.boot.system.model.bo.VisitCount">
        SELECT
            COUNT(1) AS count,
            DATE_FORMAT(create_time,'%Y-%m-%d') AS date
        FROM
            sys_log
        WHERE
            is_deleted = 0
        GROUP BY
            DATE_FORMAT(create_time, '%Y-%m-%d')
    </select>

    <!-- 获取IP日统计列表-->
    <select id="getIpCounts" resultType="com.hntsz.boot.system.model.bo.VisitCount">
        SELECT
            COUNT(DISTINCT ip) AS count,
            DATE_FORMAT(create_time, '%Y-%m-%d') AS date
        FROM
            sys_log
        WHERE
            is_deleted = 0
        GROUP BY
            DATE_FORMAT(create_time, '%Y-%m-%d')
    </select>

    <!-- 获取访问量(PV)统计数据 -->
    <select id="getPvStats" resultType="com.hntsz.boot.system.model.bo.VisitStatsBO">
        SELECT
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) AS todayCount,
            COUNT(*) AS totalCount,
            ROUND(
                CASE
                    WHEN COUNT(CASE WHEN DATE(create_time) = CURDATE() - INTERVAL 1 DAY AND TIME(create_time) &lt;= TIME(NOW()) THEN 1 END) = 0 THEN 0
                ELSE
                    (COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) -
                    COUNT(CASE WHEN DATE(create_time) = CURDATE() - INTERVAL 1 DAY AND TIME(create_time)  &lt;= TIME(NOW()) THEN 1 END)) /
                    COUNT(CASE WHEN DATE(create_time) = CURDATE() - INTERVAL 1 DAY AND TIME(create_time)  &lt;= TIME(NOW()) THEN 1 END)
                END,
            2) AS growthRate
        FROM
            sys_log
        WHERE
            is_deleted = 0
    </select>

    <!-- 获取IP统计数据 -->
    <select id="getUvStats" resultType="com.hntsz.boot.system.model.bo.VisitStatsBO">
        SELECT
            COUNT(DISTINCT CASE WHEN DATE(create_time) = CURDATE() THEN ip END) AS todayCount,
            COUNT(DISTINCT ip) AS totalCount,
            ROUND(
                CASE
                    WHEN COUNT(DISTINCT CASE WHEN DATE(create_time) = CURDATE() - INTERVAL 1 DAY AND TIME(create_time) &lt;= TIME(NOW()) THEN ip END) = 0 THEN 0
                    ELSE
                        (COUNT(DISTINCT CASE WHEN DATE(create_time) = CURDATE() THEN ip END) -
                         COUNT(DISTINCT CASE WHEN DATE(create_time) = CURDATE() - INTERVAL 1 DAY AND TIME(create_time) &lt;= TIME(NOW()) THEN ip END)) /
                        COUNT(DISTINCT CASE WHEN DATE(create_time) = CURDATE() - INTERVAL 1 DAY AND TIME(create_time) &lt;= TIME(NOW()) THEN ip END)
                    END,
                2) AS growthRate
        FROM
            sys_log
        WHERE
            is_deleted = 0
    </select>

</mapper>
