package com.hntsz.boot.modules.opportunity.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hntsz.boot.common.base.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 商机跟进记录视图对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机跟进记录视图对象")
public class OpportunityFollowVO extends BaseVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "商机ID")
    private Long opportunityId;

    @Schema(description = "商机名称")
    private String opportunityName;

    @Schema(description = "商机编码")
    private String opportunityCode;

    @Schema(description = "跟进方式")
    private String followType;

    @Schema(description = "跟进方式标签")
    private String followTypeLabel;

    @Schema(description = "跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime followDate;

    @Schema(description = "跟进时长(分钟)")
    private Integer followDuration;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "跟进内容")
    private String followContent;

    @Schema(description = "跟进结果")
    private String followResult;

    @Schema(description = "跟进结果标签")
    private String followResultLabel;

    @Schema(description = "下一步行动计划")
    private String nextAction;

    @Schema(description = "下次跟进日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate nextFollowDate;

    @Schema(description = "附件ID")
    private String attachmentId;

    @Schema(description = "跟进人ID")
    private Long followUserId;

    @Schema(description = "跟进人姓名")
    private String followUserName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}