// 认证工具类
import { localStorage, STORAGE_KEYS } from './storage'

/**
 * 获取访问令牌
 */
export function getToken(): string | null {
  return localStorage.get(STORAGE_KEYS.TOKEN)
}

/**
 * 设置访问令牌
 */
export function setToken(token: string, expire?: number): void {
  localStorage.set(STORAGE_KEYS.TOKEN, token, { expire })
}

/**
 * 移除访问令牌
 */
export function removeToken(): void {
  localStorage.remove(STORAGE_KEYS.TOKEN)
}

/**
 * 获取刷新令牌
 */
export function getRefreshToken(): string | null {
  return localStorage.get(STORAGE_KEYS.REFRESH_TOKEN)
}

/**
 * 设置刷新令牌
 */
export function setRefreshToken(token: string, expire?: number): void {
  localStorage.set(STORAGE_KEYS.REFRESH_TOKEN, token, { expire })
}

/**
 * 移除刷新令牌
 */
export function removeRefreshToken(): void {
  localStorage.remove(STORAGE_KEYS.REFRESH_TOKEN)
}

/**
 * 清除所有认证信息
 */
export function clearAuth(): void {
  removeToken()
  removeRefreshToken()
  localStorage.remove(STORAGE_KEYS.USER_INFO)
}

/**
 * 检查是否已登录
 */
export function isLoggedIn(): boolean {
  return !!getToken()
}