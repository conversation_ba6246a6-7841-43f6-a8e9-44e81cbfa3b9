package com.hntsz.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.system.model.entity.Dict;
import com.hntsz.boot.system.model.form.DictForm;
import com.hntsz.boot.system.model.vo.DictPageVO;

import org.mapstruct.Mapper;

/**
 * 字典 对象转换器
 *
 * <AUTHOR>
 * @since 2022/6/8
 */
@Mapper(componentModel = "spring")
public interface DictConverter {

    Page<DictPageVO> toPageVo(Page<Dict> page);

    DictForm toForm(Dict entity);

    Dict toEntity(DictForm entity);
}
