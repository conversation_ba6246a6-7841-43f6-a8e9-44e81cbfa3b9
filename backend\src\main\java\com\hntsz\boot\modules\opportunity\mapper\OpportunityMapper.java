package com.hntsz.boot.modules.opportunity.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.modules.opportunity.model.entity.Opportunity;
import com.hntsz.boot.modules.opportunity.model.query.OpportunityQuery;
import com.hntsz.boot.modules.opportunity.model.vo.OpportunityVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商机线索Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface OpportunityMapper extends BaseMapper<Opportunity> {

    /**
     * 分页查询商机线索
     *
     * @param page  分页参数
     * @param query 查询条件
     * @return 商机线索分页列表
     */
    Page<OpportunityVO> selectOpportunityPage(Page<OpportunityVO> page, @Param("query") OpportunityQuery query);

    /**
     * 根据ID查询商机线索详情
     *
     * @param id 商机ID
     * @return 商机线索详情
     */
    OpportunityVO selectOpportunityById(@Param("id") Long id);

    /**
     * 查询所有商机线索选项(用于下拉框)
     *
     * @return 商机线索选项列表
     */
    List<OpportunityVO> selectOpportunityOptions();

    /**
     * 根据商机编码查询商机线索
     *
     * @param opportunityCode 商机编码
     * @return 商机线索
     */
    Opportunity selectByOpportunityCode(@Param("opportunityCode") String opportunityCode);

    /**
     * 根据负责人ID查询商机线索数量
     *
     * @param responsibleUserId 负责人ID
     * @return 商机线索数量
     */
    int countByResponsibleUserId(@Param("responsibleUserId") Long responsibleUserId);

    /**
     * 根据客户ID查询商机线索数量
     *
     * @param partnerId 客户ID
     * @return 商机线索数量
     */
    int countByPartnerId(@Param("partnerId") Long partnerId);

    /**
     * 批量更新商机负责人
     *
     * @param opportunityIds    商机ID列表
     * @param responsibleUserId 新负责人ID
     * @param updateBy          更新人ID
     * @return 更新记录数
     */
    int batchUpdateResponsibleUser(@Param("opportunityIds") List<Long> opportunityIds,
                                   @Param("responsibleUserId") Long responsibleUserId,
                                   @Param("updateBy") Long updateBy);
}