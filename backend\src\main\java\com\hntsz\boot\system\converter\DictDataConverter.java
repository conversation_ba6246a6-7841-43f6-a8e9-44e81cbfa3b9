package com.hntsz.boot.system.converter;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.system.model.entity.DictItem;
import com.hntsz.boot.system.model.form.DictItemForm;
import com.hntsz.boot.system.model.vo.DictPageVO;

import org.mapstruct.Mapper;

import java.util.List;

/**
 * 字典项 对象转换器
 *
 * <AUTHOR>
 * @since 2022/6/8
 */
@Mapper(componentModel = "spring")
public interface DictDataConverter {

    Page<DictPageVO> toPageVo(Page<DictItem> page);

    DictItemForm toForm(DictItem entity);

    DictItem toEntity(DictItemForm formFata);

    Option<Long> toOption(DictItem dictItem);
    List<Option<Long>> toOption(List<DictItem> dictData);
}
