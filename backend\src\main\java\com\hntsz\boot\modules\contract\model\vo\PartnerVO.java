package com.hntsz.boot.modules.contract.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hntsz.boot.common.base.BaseVO;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * 业务伙伴视图对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
public class PartnerVO extends BaseVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 伙伴名称
     */
    private String partnerName;

    /**
     * 伙伴编码
     */
    private String partnerCode;

    /**
     * 是否我司或旗下企业(1-是 0-否)
     */
    private Boolean isOurCompany;

    /**
     * 伙伴类型(关联字典编码：partner_type)
     */
    private String partnerType;

    /**
     * 伙伴类型标签
     */
    private String partnerTypeLabel;

    /**
     * 法定代表人
     */
    private String legalRepresentative;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 地址
     */
    private String address;

    /**
     * 证件类型(关联字典编码：certificate_type)
     */
    private String certificateType;

    /**
     * 证件类型标签
     */
    private String certificateTypeLabel;

    /**
     * 证件号码
     */
    private String certificateNumber;

    /**
     * 税号
     */
    private String taxNumber;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 状态(active-正常 inactive-禁用)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
