<template>
  <el-drawer
    v-model="visible"
    title="商机详情"
    direction="rtl"
    size="800px"
    @close="handleClose"
  >
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 详情内容 -->
    <div v-else-if="Object.keys(detailData).length > 0" class="detail-content">
      <!-- 基本信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>基本信息</h3>
<!--
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleEdit"
                v-hasPerm="['opportunity:list:edit']"
              >
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete"
                v-hasPerm="['opportunity:list:delete']"
              >
                <el-icon><Delete /></el-icon>
                删除
              </el-button>
            </div>
-->
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="商机名称">
            {{ detailData.opportunityName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="商机编码">
            {{ detailData.opportunityCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="商机类型">
            {{ detailData.opportunityTypeLabel || detailData.opportunityType || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="商机来源">
            {{ detailData.opportunitySourceLabel || detailData.opportunitySource || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="商机阶段">
            <el-tag :type="getStageTagType(detailData.opportunityStage)" v-if="detailData.opportunityStage">
              {{ detailData.opportunityStageLabel || getStageLabel(detailData.opportunityStage) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityTagType(detailData.priority)" v-if="detailData.priority">
              {{ detailData.priorityLabel || getPriorityLabel(detailData.priority) }}
            </el-tag>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="预估金额">
            {{ formatAmount(detailData.estimatedAmount) }}
          </el-descriptions-item>
          <el-descriptions-item label="成单概率">
            {{ detailData.winProbability || 0 }}%
          </el-descriptions-item>
          <el-descriptions-item label="预计成交日期">
            {{ formatDate(detailData.estimatedCloseDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="负责人">
            {{ detailData.responsibleUserName || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 联系信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>联系信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="联系人">
            {{ detailData.contactPerson || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系电话">
            {{ detailData.contactPhone || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="联系邮箱">
            {{ detailData.contactEmail || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="关联客户">
            {{ detailData.partnerName || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 商机详情 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>商机详情</h3>
          </div>
        </template>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="感兴趣的产品/服务">
            {{ detailData.productInterest || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="客户需求描述">
            {{ detailData.requirements || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="竞争对手信息">
            {{ detailData.competitionInfo || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="下一步行动计划">
            {{ detailData.nextAction || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="下次跟进日期">
            {{ formatDate(detailData.nextFollowDate) }}
          </el-descriptions-item>
          <el-descriptions-item label="标签">
            {{ detailData.tags || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            {{ detailData.remark || '-' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 系统信息 -->
      <el-card shadow="never" class="section-card">
        <template #header>
          <div class="card-header">
            <h3>系统信息</h3>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(detailData.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(detailData.updateTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="跟进次数">
            {{ detailData.followCount || 0 }} 次
          </el-descriptions-item>
          <el-descriptions-item label="最后跟进时间">
            {{ formatDateTime(detailData.lastFollowDate) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { OpportunityForm } from "@/api/opportunity/opportunity.api";
import OpportunityAPI from "@/api/opportunity/opportunity.api";

defineOptions({
  name: "OpportunityDetailDrawer",
  inheritAttrs: false,
});

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  opportunityId: {
    type: [String, Number],
    default: undefined,
  },
});

const emit = defineEmits(["update:modelValue", "edit", "delete"]);

const visible = computed({
  get: () => props.modelValue,
  set: (val) => {
    emit("update:modelValue", val);
  },
});

const loading = ref(false);
const detailData = ref<OpportunityForm>({});

function loadDetailData(id: string | number) {
  if (id) {
    loading.value = true;
    OpportunityAPI.getFormData(Number(id))
      .then((response: any) => {
        console.log('详情API响应:', response);
        // API已经处理了响应结构，直接返回商机数据对象
        if (response && typeof response === 'object' && response.id) {
          // 直接是商机数据对象
          detailData.value = response;
          console.log('设置详情数据:', detailData.value);
        } else if (response.data && response.data.data) {
          // axios响应结构: response.data = { code, data: {...}, msg }
          detailData.value = response.data.data;
          console.log('设置详情数据:', detailData.value);
        } else if (response.data) {
          // 直接返回data的情况
          detailData.value = response.data;
          console.log('设置详情数据:', detailData.value);
        } else {
          console.warn('未识别的详情响应结构:', response);
          detailData.value = {};
        }
      })
      .catch((error) => {
        console.error('获取商机详情失败:', error);
        detailData.value = {};
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

function handleClose() {
  visible.value = false;
  detailData.value = {};
}

function handleEdit() {
  emit("edit", props.opportunityId);
}

function handleDelete() {
  emit("delete", props.opportunityId);
}

// 格式化方法
function formatAmount(amount: number | undefined): string {
  if (!amount) return '-';
  return `¥${amount.toLocaleString()}`;
}

function formatDate(date: string | undefined): string {
  if (!date) return '-';
  return new Date(date).toLocaleDateString();
}

function formatDateTime(dateTime: string | undefined): string {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString();
}

function getStageLabel(stage: string | undefined): string {
  const stageMap: Record<string, string> = {
    initial: '初步接触',
    interested: '有意向',
    proposal: '方案阶段',
    negotiation: '谈判阶段',
    closed_won: '成交',
    closed_lost: '失败',
  };
  return stageMap[stage || ''] || stage || '-';
}

function getStageTagType(stage: string | undefined): "primary" | "success" | "warning" | "info" | "danger" {
  const typeMap: Record<string, "primary" | "success" | "warning" | "info" | "danger"> = {
    initial: 'info',
    interested: 'warning',
    proposal: 'primary',
    negotiation: 'warning',
    closed_won: 'success',
    closed_lost: 'danger',
  };
  return typeMap[stage || ''] || 'info';
}

function getPriorityLabel(priority: string | undefined): string {
  const priorityMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低',
  };
  return priorityMap[priority || ''] || priority || '-';
}

function getPriorityTagType(priority: string | undefined): "primary" | "success" | "warning" | "info" | "danger" {
  const typeMap: Record<string, "primary" | "success" | "warning" | "info" | "danger"> = {
    high: 'danger',
    medium: 'warning',
    low: 'info',
  };
  return typeMap[priority || ''] || 'info';
}

watch(
  () => props.modelValue,
  (val) => {
    if (val && props.opportunityId) {
      loadDetailData(props.opportunityId);
    }
  }
);
</script>

<style scoped>
.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 20px;
}

.section-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.loading-container {
  padding: 20px;
}
</style>
