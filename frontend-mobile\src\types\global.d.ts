// 全局类型定义

// 分页查询参数基类
export interface PageQuery {
  pageNum?: number
  pageSize?: number
  sort?: string
  order?: string
}

// 分页结果
export interface PageResult<T> {
  records: T
  total: number
  current: number
  size: number
  pages: number
}

// 选项类型
export interface OptionType {
  label: string
  value: string | number
  disabled?: boolean
  children?: OptionType[]
}

// API响应基类
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 用户信息
export interface UserInfo {
  id: number
  username: string
  nickname: string
  avatar?: string
  mobile?: string
  email?: string
  deptId?: number
  deptName?: string
  roles: string[]
  permissions: string[]
}

// 上传文件信息
export interface FileInfo {
  id: string
  name: string
  url: string
  size: number
  type: string
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  message?: string
  trigger?: string | string[]
  validator?: (rule: any, value: any, callback: any) => void
}

declare global {
  interface Window {
    __INITIAL_STATE__?: any
  }
}

export {}