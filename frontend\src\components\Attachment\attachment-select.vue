<!-- 列表选择器示例 -->
<script setup lang="ts">
import type { ISelectConfig } from "@/components/TableSelect/index.vue";
import TableSelect from "@/components/TableSelect/index.vue";
import FileAPI from "@/api/file.api";
import { AttachmentVO } from "@/api/attachment/attachment.api";
import AttachmentAPI from "@/api/attachment/attachment.api";
import AttachmentUploader from "@/components/Attachment/uploader.vue";
import { Edit, Close } from "@element-plus/icons-vue";
import { ref, watch } from "vue";

const dialogVisible = ref(false);
const selected = ref<AttachmentVO[]>([]);
const tableSelectRef = ref<InstanceType<typeof TableSelect>>();

const props = defineProps<{
  fileType?: "IMAGE" | "VIDEO" | "AUDIO" | "DOCUMENT" | "OTHER";
  multiple?: boolean;
  modelValue?: AttachmentVO[] | AttachmentVO;
  autoShowPopover?: boolean;
}>();

const emit = defineEmits<{
  (_e: "update:modelValue", _value: AttachmentVO[] | AttachmentVO): void;
}>();

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      selected.value = Array.isArray(newVal) ? newVal : [newVal];
    } else {
      selected.value = [];
    }
  },
  { immediate: true }
);

function handleConfirm(data: AttachmentVO[]) {
  selected.value = data;
  if (props.multiple) {
    emit("update:modelValue", selected.value);
  } else {
    emit("update:modelValue", selected.value[0]);
  }
}

const selectConfig: ISelectConfig = {
  pk: "id",
  width: "70%",
  placeholder: "请选择附件",
  multiple: props.multiple,
  formItems: [
    {
      type: "input" as const,
      label: "关键字",
      prop: "fileName",
      attrs: {
        placeholder: "文件名",
        clearable: true,
        style: {
          width: "200px",
        },
      },
    },
    ...(props.fileType
      ? []
      : [
          {
            type: "select" as const,
            label: "文件类型",
            prop: "fileType",
            attrs: {
              placeholder: "全部",
              clearable: true,
              style: {
                width: "100px",
              },
            },
            options: [
              { label: "图片", value: "IMAGE" },
              { label: "视频", value: "VIDEO" },
              { label: "文档", value: "DOCUMENT" },
              { label: "音频", value: "AUDIO" },
              { label: "其他", value: "OTHER" },
            ],
          },
        ]),
  ],
  indexAction: function (params) {
    if (props.fileType) {
      params.fileType = props.fileType;
    }
    return AttachmentAPI.getList(params);
  },
  tableColumns: [
    { type: "selection", width: 50, align: "center" },
    { label: "Id", align: "center", prop: "id", width: 100, show: true },
    { label: "内容", align: "center", prop: "filePath", templet: "custom" },
    { label: "文件名", align: "center", prop: "fileName" },
    { label: "文件类型", align: "center", prop: "fileType", templet: "custom" },
    { label: "文件大小", align: "center", prop: "fileSize", templet: "custom" },
  ],
};

const handleUpload = () => {
  dialogVisible.value = true;
};

const handleSuccess = () => {
  tableSelectRef.value?.fetchPageData();
};

const handleMove = (item: AttachmentVO, direction: number) => {
  const index = selected.value.indexOf(item);
  if (index !== -1) {
    selected.value.splice(index, 1);
  }
  selected.value.splice(index + direction, 0, item);
};
</script>

<template>
  <div class="w-full">
    <AttachmentUploader :dialog-visible="dialogVisible" @success="handleSuccess" />
    <table-select
      ref="tableSelectRef"
      :auto-show-popover="props.autoShowPopover ?? false"
      :select-config="selectConfig"
      @confirm-click="handleConfirm"
    >
      <template #default>
        <div v-if="selected.length > 0 && props.multiple">
          <div
            v-for="item in selected"
            :key="item.id"
            class="flex items-center justify-between w-[420px] px-2 border border-gray-200 rounded mb-2"
          >
            <div class="flex items-center">
              <a :href="FileAPI.getFullUrl(item.filePath ?? '')" target="_blank">
                <el-icon v-if="item.fileType === 'IMAGE'" class="mr-2"><Picture /></el-icon>
                <el-icon v-else-if="item.fileType === 'VIDEO'" class="mr-2">
                  <VideoCamera />
                </el-icon>
                <el-icon v-else-if="item.fileType === 'DOCUMENT'" class="mr-2">
                  <Document />
                </el-icon>
                <el-icon v-else-if="item.fileType === 'AUDIO'" class="mr-2"><VideoPlay /></el-icon>
                <el-icon v-else class="mr-2"><InfoFilled /></el-icon>
                <span>{{ item.fileName }}</span>
              </a>
            </div>
            <div>
              <el-button size="small" icon="ArrowUp" circle @click="handleMove(item, -1)" />
              <el-button size="small" icon="ArrowDown" circle @click="handleMove(item, 1)" />
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                circle
                @click="selected = selected.filter((file) => file.id !== item.id)"
              />
            </div>
          </div>
        </div>
        <div v-else-if="selected.length > 0" class="flex flex-row max-w-[600px] max-h-[400px]">
          <div v-if="selected[0].fileType === 'IMAGE'" class="cursor-pointer">
            <el-image
              class="max-w-[600px] max-h-[400px] border rounded-md border-gray-300"
              :src="FileAPI.getFullUrl(selected[0].filePath ?? '')"
            />
          </div>
          <div v-else-if="selected[0].fileType === 'VIDEO'">
            <video
              class="max-w-[600px] max-h-[400px] border rounded-md border-gray-300"
              preload="none"
              controls
              :src="FileAPI.getFullUrl(selected[0].filePath ?? '')"
            />
          </div>
          <div class="flex flex-col gap-2 justify-center ml-2">
            <div>
              <el-button
                type="primary"
                :icon="Edit"
                circle
                title="编辑"
                @click="tableSelectRef?.handleShowPopover()"
              />
            </div>
            <div>
              <el-button
                type="info"
                :icon="Close"
                circle
                title="删除"
                @click="tableSelectRef?.handleClearSelected()"
              />
            </div>
          </div>
        </div>
        <div v-else>
          <div
            v-if="props.fileType === 'IMAGE' || props.fileType === 'VIDEO'"
            class="w-[100px] h-[100px] flex flex-col items-center justify-center border border-gray-300 rounded-md gap-2 cursor-pointer"
            @click="tableSelectRef?.handleShowPopover()"
          >
            <el-icon :size="32" color="#409EFF"><UploadFilled /></el-icon>
            <span class="text-xs text-gray-500">点击上传</span>
          </div>
          <div v-else>
            <el-button
              type="primary"
              icon="UploadFilled"
              @click="tableSelectRef?.handleShowPopover()"
            >
              上传附件
            </el-button>
          </div>
        </div>
      </template>
      <template #prefixForm>
        <el-form-item>
          <el-button type="primary" icon="upload" @click="handleUpload">上传新文件</el-button>
        </el-form-item>
      </template>
      <template #filePath="{ row }">
        <div v-if="row.fileType === 'IMAGE'">
          <el-image
            style="width: 100px; height: 100px"
            :src="FileAPI.getFullUrl(row.filePath)"
            fit="cover"
          />
        </div>
        <div v-else-if="row.fileType === 'VIDEO'">
          <video
            style="width: 100px; height: 100px"
            :src="FileAPI.getFullUrl(row.filePath)"
            controls
            preload="none"
          />
        </div>
        <div v-else-if="row.fileType === 'AUDIO'">
          <audio
            style="width: 100px; height: 100px"
            :src="FileAPI.getFullUrl(row.filePath)"
            controls
          />
        </div>
        <div v-else>
          <a :href="FileAPI.getFullUrl(row.filePath)" target="_blank">下载</a>
        </div>
      </template>
      <template #fileType="{ row }">
        <div v-if="row.fileType === 'IMAGE'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <Picture />
          </el-icon>
          <span>图片</span>
        </div>
        <div v-else-if="row.fileType === 'VIDEO'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <VideoCamera />
          </el-icon>
          <span>视频</span>
        </div>
        <div v-else-if="row.fileType === 'DOCUMENT'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <Document />
          </el-icon>
          <span>文档</span>
        </div>
        <div v-else-if="row.fileType === 'AUDIO'" class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <VideoPlay />
          </el-icon>
          <span>音频</span>
        </div>
        <div v-else class="flex items-center justify-center gap-2">
          <el-icon :size="16" color="#409EFF">
            <InfoFilled />
          </el-icon>
          <span>其他</span>
        </div>
      </template>
      <template #fileSize="{ row }">
        <div>{{ (row.fileSize / (1024 * 1024)).toFixed(2) }} MB</div>
      </template>
    </table-select>
  </div>
</template>
