package com.hntsz.boot.modules.contract.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hntsz.boot.common.model.Option;
import com.hntsz.boot.modules.contract.model.entity.Partner;
import com.hntsz.boot.modules.contract.model.form.PartnerForm;
import com.hntsz.boot.modules.contract.model.query.PartnerQuery;
import com.hntsz.boot.modules.contract.model.vo.PartnerVO;

import java.util.List;

/**
 * 业务伙伴服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface PartnerService extends IService<Partner> {

    /**
     * 获取业务伙伴分页列表
     *
     * @param query 查询参数
     * @return 业务伙伴分页列表
     */
    IPage<PartnerVO> getPartnerPage(PartnerQuery query);

    /**
     * 获取业务伙伴详情
     *
     * @param id 业务伙伴ID
     * @return 业务伙伴详情
     */
    PartnerVO getPartnerDetail(Long id);

    /**
     * 获取业务伙伴表单数据
     *
     * @param id 业务伙伴ID
     * @return 业务伙伴表单数据
     */
    PartnerForm getPartnerFormData(Long id);

    /**
     * 新增业务伙伴
     *
     * @param form 业务伙伴表单数据
     * @return 是否成功
     */
    boolean savePartner(PartnerForm form);

    /**
     * 更新业务伙伴
     *
     * @param id 业务伙伴ID
     * @param form 业务伙伴表单数据
     * @return 是否成功
     */
    boolean updatePartner(Long id, PartnerForm form);

    /**
     * 删除业务伙伴
     *
     * @param ids 业务伙伴ID数组
     * @return 是否成功
     */
    boolean deletePartners(Long[] ids);

    /**
     * 获取业务伙伴选项列表
     *
     * @return 业务伙伴选项列表
     */
    List<Option> getPartnerOptions();

    /**
     * 根据伙伴名称查询伙伴
     *
     * @param partnerName 伙伴名称
     * @return 业务伙伴
     */
    Partner getByPartnerName(String partnerName);

    /**
     * 根据伙伴编码查询伙伴
     *
     * @param partnerCode 伙伴编码
     * @return 业务伙伴
     */
    Partner getByPartnerCode(String partnerCode);

    /**
     * 检查伙伴名称是否存在
     *
     * @param partnerName 伙伴名称
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByPartnerName(String partnerName, Long excludeId);

    /**
     * 检查伙伴编码是否存在
     *
     * @param partnerCode 伙伴编码
     * @param excludeId 排除的ID
     * @return 是否存在
     */
    boolean existsByPartnerCode(String partnerCode, Long excludeId);
}
