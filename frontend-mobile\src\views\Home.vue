<template>
  <div class="home-page">
    <!-- 顶部欢迎区域 -->
    <div class="home-header">
      <div class="user-info">
        <van-image
          :src="userStore.avatar || '/avatar-placeholder.png'"
          alt="头像"
          class="avatar"
          round
        />
        <div class="user-text">
          <div class="greeting">{{ greeting }}</div>
          <div class="username">{{ userStore.nickname || userStore.username }}</div>
        </div>
      </div>
      <van-icon name="bell-o" size="20" class="bell-icon" @click="showNotifications" />
    </div>

    <!-- 数据统计 -->
    <div class="stats-section">
      <div class="stats-card">
        <div class="stats-item" @click="goToOpportunity">
          <div class="stats-number">{{ stats.opportunityCount }}</div>
          <div class="stats-label">我的商机</div>
        </div>
        <div class="stats-divider"></div>
        <div class="stats-item" @click="goToContract">
          <div class="stats-number">{{ stats.contractCount }}</div>
          <div class="stats-label">我的合同</div>
        </div>
        <div class="stats-divider"></div>
        <div class="stats-item" @click="goToFollow">
          <div class="stats-number">{{ stats.followCount }}</div>
          <div class="stats-label">待跟进</div>
        </div>
      </div>
    </div>

    <!-- 快捷功能 -->
    <div class="quick-actions">
      <div class="section-title">快捷功能</div>
      <div class="action-grid">
        <div class="action-item" @click="createOpportunity">
          <van-icon name="plus" class="action-icon" />
          <span class="action-text">新增商机</span>
        </div>
        <div class="action-item" @click="createContract">
          <van-icon name="notes-o" class="action-icon" />
          <span class="action-text">新增合同</span>
        </div>
        <div class="action-item" @click="createPartner">
          <van-icon name="contact" class="action-icon" />
          <span class="action-text">新增伙伴</span>
        </div>
        <div class="action-item" @click="scanCode">
          <van-icon name="scan" class="action-icon" />
          <span class="action-text">扫一扫</span>
        </div>
      </div>
    </div>

    <!-- 最近动态 -->
    <div class="recent-activities">
      <div class="section-title">最近动态</div>
      <div v-if="activities.length > 0" class="activity-list">
        <div
          v-for="activity in activities"
          :key="activity.id"
          class="activity-item"
          @click="goToActivityDetail(activity)"
        >
          <div class="activity-icon">
            <van-icon :name="getActivityIcon(activity.type)" />
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-desc">{{ activity.description }}</div>
            <div class="activity-time">{{ formatRelativeTime(activity.createTime) }}</div>
          </div>
        </div>
      </div>
      <van-empty v-else description="暂无动态" />
    </div>

    <!-- 底部导航 -->
    <van-tabbar v-model="activeTab" @change="onTabChange">
      <van-tabbar-item icon="home-o" to="/home">首页</van-tabbar-item>
      <van-tabbar-item icon="chart-trending-o" to="/opportunity">商机</van-tabbar-item>
      <van-tabbar-item icon="description" to="/contract">合同</van-tabbar-item>
      <van-tabbar-item icon="contact" to="/partner">伙伴</van-tabbar-item>
      <van-tabbar-item icon="user-o" to="/profile">我的</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showNotify } from 'vant'
import { useUserStore } from '@/stores/user'
import { formatRelativeTime } from '@/utils/format'

const router = useRouter()
const userStore = useUserStore()

// 当前选中的标签
const activeTab = ref(0)

// 统计数据
const stats = ref({
  opportunityCount: 0,
  contractCount: 0,
  followCount: 0
})

// 最近动态
const activities = ref<any[]>([])

// 问候语
const greeting = computed(() => {
  const hour = new Date().getHours()
  if (hour < 6) return '夜深了'
  if (hour < 9) return '早上好'
  if (hour < 12) return '上午好'
  if (hour < 18) return '下午好'
  if (hour < 22) return '晚上好'
  return '夜深了'
})

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap: Record<string, string> = {
    opportunity: 'chart-trending-o',
    contract: 'description',
    follow: 'chat-o',
    partner: 'contact',
    payment: 'gold-coin-o'
  }
  return iconMap[type] || 'info-o'
}

// 导航到商机页面
const goToOpportunity = () => {
  router.push('/opportunity')
}

// 导航到合同页面
const goToContract = () => {
  router.push('/contract')
}

// 导航到跟进页面
const goToFollow = () => {
  router.push('/opportunity?tab=follow')
}

// 创建商机
const createOpportunity = () => {
  router.push('/opportunity/create')
}

// 创建合同
const createContract = () => {
  router.push('/contract/create')
}

// 创建伙伴
const createPartner = () => {
  router.push('/partner/create')
}

// 扫码功能
const scanCode = () => {
  showNotify({
    type: 'primary',
    message: '扫码功能开发中...'
  })
}

// 显示通知
const showNotifications = () => {
  showNotify({
    type: 'primary',
    message: '通知功能开发中...'
  })
}

// 跳转到活动详情
const goToActivityDetail = (activity: any) => {
  const routeMap: Record<string, string> = {
    opportunity: `/opportunity/${activity.relatedId}`,
    contract: `/contract/${activity.relatedId}`,
    partner: `/partner/${activity.relatedId}`
  }
  
  const route = routeMap[activity.type]
  if (route) {
    router.push(route)
  }
}

// 标签切换
const onTabChange = (index: number) => {
  activeTab.value = index
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用API获取统计数据
    // 暂时使用模拟数据
    stats.value = {
      opportunityCount: 15,
      contractCount: 8,
      followCount: 3
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 加载最近动态
const loadActivities = async () => {
  try {
    // 这里应该调用API获取最近动态
    // 暂时使用模拟数据
    activities.value = [
      {
        id: 1,
        type: 'opportunity',
        title: '新增商机',
        description: '华为技术有限公司 - 云服务器采购项目',
        relatedId: '1',
        createTime: new Date(Date.now() - 1000 * 60 * 30).toISOString() // 30分钟前
      },
      {
        id: 2,
        type: 'follow',
        title: '跟进记录',
        description: '电话沟通客户需求，客户表示有兴趣',
        relatedId: '1',
        createTime: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString() // 2小时前
      },
      {
        id: 3,
        type: 'contract',
        title: '合同签署',
        description: '软件开发服务合同已完成签署',
        relatedId: '1',
        createTime: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString() // 1天前
      }
    ]
  } catch (error) {
    console.error('加载最近动态失败:', error)
  }
}

// 页面初始化
onMounted(() => {
  loadStats()
  loadActivities()
})
</script>

<style scoped lang="scss">
.home-page {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 60px; // 为底部导航留出空间
}

.home-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px 16px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  
  .user-info {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 50px;
      height: 50px;
      margin-right: 12px;
    }
    
    .user-text {
      .greeting {
        font-size: 14px;
        opacity: 0.8;
        margin-bottom: 4px;
      }
      
      .username {
        font-size: 18px;
        font-weight: 500;
      }
    }
  }
  
  .bell-icon {
    color: white;
    opacity: 0.8;
  }
}

.stats-section {
  padding: 0 16px;
  margin-top: -20px;
  
  .stats-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    
    .stats-item {
      flex: 1;
      text-align: center;
      cursor: pointer;
      
      .stats-number {
        font-size: 24px;
        font-weight: 600;
        color: #323233;
        margin-bottom: 8px;
      }
      
      .stats-label {
        font-size: 14px;
        color: #969799;
      }
    }
    
    .stats-divider {
      width: 1px;
      background-color: #ebedf0;
      margin: 0 20px;
    }
  }
}

.quick-actions {
  padding: 20px 16px;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 16px;
  }
  
  .action-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    
    .action-item {
      background: white;
      border-radius: 12px;
      padding: 20px 16px;
      text-align: center;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: transform 0.2s;
      
      &:active {
        transform: scale(0.95);
      }
      
      .action-icon {
        font-size: 24px;
        color: #1989fa;
        margin-bottom: 8px;
      }
      
      .action-text {
        font-size: 12px;
        color: #646566;
      }
    }
  }
}

.recent-activities {
  padding: 0 16px 20px;
  
  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #323233;
    margin-bottom: 16px;
  }
  
  .activity-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    
    .activity-item {
      display: flex;
      padding: 16px;
      border-bottom: 1px solid #ebedf0;
      cursor: pointer;
      
      &:last-child {
        border-bottom: none;
      }
      
      &:active {
        background-color: #f8f8f8;
      }
      
      .activity-icon {
        width: 40px;
        height: 40px;
        background-color: #f0f1ff;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        
        :deep(.van-icon) {
          color: #1989fa;
          font-size: 18px;
        }
      }
      
      .activity-content {
        flex: 1;
        
        .activity-title {
          font-size: 14px;
          font-weight: 500;
          color: #323233;
          margin-bottom: 4px;
        }
        
        .activity-desc {
          font-size: 13px;
          color: #646566;
          margin-bottom: 8px;
          line-height: 1.4;
        }
        
        .activity-time {
          font-size: 12px;
          color: #969799;
        }
      }
    }
  }
}
</style>