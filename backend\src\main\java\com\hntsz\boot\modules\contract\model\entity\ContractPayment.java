package com.hntsz.boot.modules.contract.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.hntsz.boot.common.base.BaseEntityExtra;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 合同付款记录表实体对象
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Getter
@Setter
@TableName("contract_payment")
public class ContractPayment extends BaseEntityExtra {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 合同ID
     */
    private Long contractId;

    /**
     * 付款单号
     */
    private String paymentNo;

    /**
     * 付款类型(关联字典编码：payment_type)
     */
    private String paymentType;

    /**
     * 付款方式(关联字典编码：payment_method)
     */
    private String paymentMethod;

    /**
     * 付款方ID(关联partner表)
     */
    private Long payerPartnerId;

    /**
     * 收款方ID(关联partner表)
     */
    private Long payeePartnerId;

    /**
     * 计划金额
     */
    private BigDecimal plannedAmount;

    /**
     * 实际金额
     */
    private BigDecimal actualAmount;

    /**
     * 币种
     */
    private String currency;

    /**
     * 计划付款日期
     */
    private LocalDate plannedDate;

    /**
     * 实际付款日期
     */
    private LocalDate actualDate;

    /**
     * 付款状态(pending-待付款 paid-已付款 partial-部分付款 overdue-已逾期 cancelled-已取消)
     */
    private String paymentStatus;

    /**
     * 付款银行
     */
    private String bankName;

    /**
     * 付款账号
     */
    private String bankAccount;

    /**
     * 交易流水号
     */
    private String transactionNo;

    /**
     * 付款凭证文件Id
     */
    private String voucherAttachmentId;

    /**
     * 发票状态(not_issued-未开票 issued-已开票 received-已收票)
     */
    private String invoiceStatus;

    /**
     * 发票号码
     */
    private String invoiceNo;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 开票日期
     */
    private LocalDate invoiceDate;

    /**
     * 备注
     */
    private String remark;
}
