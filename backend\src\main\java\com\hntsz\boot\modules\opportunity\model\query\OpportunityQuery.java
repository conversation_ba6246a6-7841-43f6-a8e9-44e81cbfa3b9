package com.hntsz.boot.modules.opportunity.model.query;

import com.hntsz.boot.common.base.BasePageQuery;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商机线索查询参数
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Schema(description = "商机线索查询参数")
public class OpportunityQuery extends BasePageQuery {

    @Schema(description = "商机编码")
    private String opportunityCode;

    @Schema(description = "商机名称")
    private String opportunityName;

    @Schema(description = "商机类型")
    private String opportunityType;

    @Schema(description = "商机来源")
    private String opportunitySource;

    @Schema(description = "关联客户ID")
    private Long partnerId;

    @Schema(description = "商机阶段")
    private String opportunityStage;

    @Schema(description = "商机状态")
    private String opportunityStatus;

    @Schema(description = "优先级")
    private String priority;

    @Schema(description = "负责人ID")
    private Long responsibleUserId;

    @Schema(description = "所属部门ID")
    private Long deptId;

    @Schema(description = "预估金额最小值")
    private BigDecimal minEstimatedAmount;

    @Schema(description = "预估金额最大值")
    private BigDecimal maxEstimatedAmount;

    @Schema(description = "成单概率最小值")
    private Integer minWinProbability;

    @Schema(description = "成单概率最大值")
    private Integer maxWinProbability;

    @Schema(description = "预计成交开始日期")
    private LocalDate estimatedCloseStartDate;

    @Schema(description = "预计成交结束日期")
    private LocalDate estimatedCloseEndDate;

    @Schema(description = "实际成交开始日期")
    private LocalDate actualCloseStartDate;

    @Schema(description = "实际成交结束日期")
    private LocalDate actualCloseEndDate;

    @Schema(description = "创建开始时间")
    private LocalDate createStartTime;

    @Schema(description = "创建结束时间")
    private LocalDate createEndTime;

    @Schema(description = "失败原因")
    private String lostReason;

    @Schema(description = "标签")
    private String tags;

    @Schema(description = "关键词搜索(模糊匹配商机名称、编码、联系人、客户名称、产品兴趣、需求描述)")
    private String keywords;
}