package com.hntsz.boot.modules.attachment.converter;

import org.mapstruct.Mapper;

import com.hntsz.boot.common.base.ICrudConverter;
import com.hntsz.boot.modules.attachment.model.entity.Attachment;
import com.hntsz.boot.modules.attachment.model.vo.AttachmentVO;

@Mapper(componentModel = "spring")
public interface AttachmentConverter
        extends ICrudConverter<Attachment, AttachmentVO, AttachmentVO, AttachmentVO, AttachmentVO> {
}
